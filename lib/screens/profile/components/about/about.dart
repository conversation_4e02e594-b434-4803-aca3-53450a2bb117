import 'package:ako_basma/screens/profile/components/settings/custom_settings_tile.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:flutter/material.dart';

class About extends StatefulWidget {
  const About({super.key});

  @override
  State<StatefulWidget> createState() {
    return _AboutState();
  }
}

class _AboutState extends State<About> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsetsDirectional.fromSTEB(0, 12, 0, 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            localization.about,
            style: textStyles.body2.copyWith(
              color: colors.secondaryText,
              fontWeight: FontWeight.w600,
            ),
          ),
          Container(
            margin: const EdgeInsetsDirectional.only(top: 8),
            child: Column(
              children: [
                CustomSettingsTile(
                  title: localization.aboutUs,
                  hasToggle: false,
                  onTap: () {
                    // Handle privacy policy tap
                    print('About Us tapped');
                  },
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(top: 8),
                ),
                CustomSettingsTile(
                  title: localization.termsOfUse,
                  hasToggle: false,
                  onTap: () {
                    // Handle terms of service tap
                    print('Terms of Use tapped');
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
