import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/labels.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/message_bubble.dart';

/// HR Messages list component
class HrMessagesList extends StatelessWidget {
  const HrMessagesList({super.key});

  // Sample HR chat data
  List<Map<String, dynamic>> _getHrMessages(AppLocalizations localization) {
    return [
      {
        'id': '1',
        'message': localization.hrGreeting,
        'time': '10:10',
        'isSentByMe': false, // From HR
        'isRead': true,
        'profileImage': '',
      },
      {
        'id': '2',
        'message': localization.hrSampleMessage,
        'time': '10:11',
        'isSentByMe': false, // From HR
        'isRead': true,
        'profileImage': '',
      },
    ];
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final localization = AppLocalizations.of(context)!;
    final messages = _getHrMessages(localization);

    return Container(
      color: colors.background,
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        itemCount: messages.length,
        itemBuilder: (context, index) {
          final message = messages[index];

          return Column(
            children: [
              // Message bubble with HR styling
              MessageBubble(
                message: message['message'],
                time: message['time'],
                isSentByMe: message['isSentByMe'],
                isRead: message['isRead'],
                profileImage: message['profileImage'],
                showAvatar: false, // No avatars needed for HR chat
              ),

              // Add spacing between messages
              Container(
                margin: const EdgeInsets.only(bottom: 8),
              ),
            ],
          );
        },
      ),
    );
  }
}
