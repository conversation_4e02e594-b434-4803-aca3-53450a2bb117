import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/profile/components/settings/chat_with_hr.dart';
import 'package:ako_basma/screens/profile/components/settings/custom_settings_tile.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/providers/theme/theme_provider.dart';
import 'package:ako_basma/providers/language/language_provider.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class Settings extends ConsumerStatefulWidget {
  const Settings({super.key});

  @override
  ConsumerState<Settings> createState() => _SettingsState();
}

class _SettingsState extends ConsumerState<Settings> {
  // State variable for notifications toggle
  bool _notificationsEnabled = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Container(
      padding: const EdgeInsetsDirectional.fromSTEB(0, 12, 0, 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.settings,
            style: textStyles.body2.copyWith(
              color: colors.secondaryText,
              fontWeight: FontWeight.w600,
            ),
          ),
          Container(
            margin: const EdgeInsetsDirectional.only(top: 8),
            child: Column(
              children: [
                CustomSettingsTile(
                  title: AppLocalizations.of(context)!.notifications,
                  hasToggle: true,
                  toggleValue: _notificationsEnabled,
                  onToggleChanged: (value) {
                    setState(() {
                      _notificationsEnabled = value;
                    });
                  },
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(top: 8),
                ),
                // Theme mode tile with toggle
                Consumer(
                  builder: (context, ref, child) {
                    // Watch the current theme mode from provider
                    final isDarkMode = ref.watch(isDarkModeProvider);

                    return CustomSettingsTile(
                      title: AppLocalizations.of(context)!.themeMode,
                      hasToggle: true,
                      toggleValue: isDarkMode,
                      onToggleChanged: (value) async {
                        // Update theme mode through provider
                        await ref
                            .read(themeModeProvider.notifier)
                            .toggleTheme(value);
                      },
                    );
                  },
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(top: 8),
                ),
                // Language tile with functional dropdown
                Consumer(
                  builder: (context, ref, child) {
                    final languageNotifier =
                        ref.watch(languageProvider.notifier);
                    final currentLanguage = ref.watch(languageProvider);

                    return Container(
                      height: 56,
                      padding:
                          const EdgeInsetsDirectional.symmetric(horizontal: 16),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.primaryVariant,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              AppLocalizations.of(context)!.language,
                              style: textStyles.body.copyWith(
                                color: colors.secondaryText,
                              ),
                            ),
                          ),
                          Container(
                            margin: const EdgeInsetsDirectional.only(top: 4),
                            child: DropdownButton<String>(
                              value: currentLanguage.languageCode,
                              icon: Icon(
                                Icons.keyboard_arrow_down,
                                color: colors.primary,
                              ),
                              underline: Container(),
                              style: textStyles.body2.copyWith(
                                color: colors.tertiaryText,
                              ),
                              dropdownColor: colors.backgroundContainer,
                              items: const [
                                DropdownMenuItem(
                                  value: 'en',
                                  child: Text('English'),
                                ),
                                DropdownMenuItem(
                                  value: 'ar',
                                  child: Text('العربية'),
                                ),
                              ],
                              onChanged: (String? newValue) async {
                                if (newValue != null) {
                                  // Change language using provider
                                  await languageNotifier
                                      .changeLanguage(newValue);
                                }
                              },
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(top: 8),
                ),
                // Report A Problem tile (no toggle)
                CustomSettingsTile(
                  title: AppLocalizations.of(context)!.reportAProblem,
                  onTap: () {
                    // Handle report a problem tap
                    print('Report A Problem tapped');
                  },
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(top: 8),
                ),
                // Chat With HR tile (no toggle)
                CustomSettingsTile(
                  title: AppLocalizations.of(context)!.chatWithHR,
                  onTap: () {
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return const ChatWithHR();
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
