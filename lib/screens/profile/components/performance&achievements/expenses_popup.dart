import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/components/search bar/search_bar.dart';
import 'package:ako_basma/screens/profile/components/performance&achievements/expenses_card.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';
import 'package:flutter/material.dart';

class ExpensesPopup extends StatefulWidget {
  final VoidCallback? onBack;

  const ExpensesPopup({super.key, this.onBack});

  @override
  State<ExpensesPopup> createState() => _ExpensesPopupState();
}

class _ExpensesPopupState extends State<ExpensesPopup> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        // Header with back button and title
        Container(
          padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
          child: Row(
            children: [
              GestureDetector(
                onTap: () {
                  if (widget.onBack != null) {
                    widget.onBack!();
                  } else {
                    Navigator.of(context).pop();
                  }
                },
                child: Container(
                  padding: const EdgeInsetsDirectional.fromSTEB(8, 8, 8, 8),
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colors.strokeColor,
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    DirectionHelpers.getBackArrowIcon(context),
                    color: colors.primaryText,
                    size: 24,
                  ),
                ),
              ),
              Container(
                margin: const EdgeInsetsDirectional.fromSTEB(12, 0, 0, 0),
              ),
              Text(
                localization.expenses,
                style: textStyles.headline3.copyWith(
                  color: colors.secondaryText,
                ),
              ),
            ],
          ),
        ),
        // Main content - scrollable
        Expanded(
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Container(
              padding: const EdgeInsetsDirectional.fromSTEB(8, 0, 8, 0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  CustomSearchBar(
                    bgColor: colors.backgroundContainer,
                  ),
                  Container(height: 10),
                  ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: 5,
                    separatorBuilder: (context, index) => Container(
                      margin: const EdgeInsetsDirectional.fromSTEB(0, 5, 0, 5),
                    ),
                    itemBuilder: (context, index) => const ExpensesCard(),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Save button at bottom
        // Container(
        //   padding: const EdgeInsets.all(16),
        //   child: SizedBox(
        //     width: double.infinity,
        //     height: 48,
        //     child: ElevatedButton(
        //       onPressed: () {
        //         Navigator.of(context).pop();
        //       },
        //       style: ElevatedButton.styleFrom(
        //         backgroundColor: colors.primary,
        //         foregroundColor: colors.primaryText,
        //         shape: RoundedRectangleBorder(
        //           borderRadius: BorderRadius.circular(16),
        //         ),
        //       ),
        //       child: Text(
        //         Labels.save,
        //         style: textStyles.headline2.copyWith(
        //           color: colors.primaryText,
        //         ),
        //       ),
        //     ),
        //   ),
        // ),
      ],
    );
  }
}
