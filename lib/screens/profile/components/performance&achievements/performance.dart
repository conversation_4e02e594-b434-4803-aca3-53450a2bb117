import 'package:ako_basma/screens/profile/components/performance&achievements/expenses_popup.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:solar_icons/solar_icons.dart';

class Performance extends StatefulWidget {
  const Performance({super.key});

  @override
  State<Performance> createState() => _PerformanceState();
}

class _PerformanceState extends State<Performance> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    return Container(
      margin: const EdgeInsetsDirectional.only(top: 14),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            localization.performanceAndAchievements,
            style: textStyles.body2.copyWith(
              color: colors.secondaryText,
              fontWeight: FontWeight.w600,
            ),
          ),
          // Overtime Card
          Container(
            margin: const EdgeInsetsDirectional.only(top: 10),
            child: Container(
              width: MediaQuery.of(context).size.width - 32,
              padding: const EdgeInsetsDirectional.fromSTEB(12, 12, 12, 12),
              decoration: BoxDecoration(
                color: colors.primaryVariant,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  width: 1,
                  color: colors.primaryVariant,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        SolarIconsOutline.clockCircle,
                        color: colors.primary,
                        size: 20,
                      ),
                      Container(
                        margin: const EdgeInsetsDirectional.only(start: 8),
                      ),
                      Text(
                        localization.overTime,
                        style: textStyles.body2.copyWith(
                          color: colors.primary,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    margin: const EdgeInsetsDirectional.only(top: 8, start: 2),
                    child: Text(
                      localization.overtimeHours(12),
                      style: textStyles.body.copyWith(
                        color: colors.primaryText,
                        fontSize: 18,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Performance Metrics Grid
          Container(
            margin: const EdgeInsetsDirectional.only(top: 12),
            width: MediaQuery.of(context).size.width - 32,
            child: Column(
              children: [
                // First Row
                Row(
                  children: [
                    Expanded(
                      child: _buildMetricCard(
                        context,
                        '45',
                        localization.completedTasks,
                        colors.success, // Green color
                        colors,
                        BoxDecoration(
                          color: colors.successContainer,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        textStyles,
                      ),
                    ),
                    Container(
                        margin: const EdgeInsetsDirectional.only(start: 8)),
                    Expanded(
                      child: _buildMetricCard(
                        context,
                        '5',
                        localization.timeOffTaken,
                        colors.warning, // Orange color
                        colors,
                        BoxDecoration(
                          color: colors.warningContainer,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        textStyles,
                        subtitle: localization.days,
                      ),
                    ),
                  ],
                ),
                Container(margin: const EdgeInsetsDirectional.only(top: 8)),
                // Second Row
                Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          showAdaptivePopup(
                            context,
                            (ctx, sc) => ExpensesPopup(
                              onBack: () => Navigator.pop(ctx),
                            ),
                            isDismissible: false,
                            scrollable: true,
                            contentPadding: EdgeInsets.zero,
                            topRadius: 0,
                            fullScreen: true,
                            useRootNavigator: true,
                          );
                        },
                        child: _buildMetricCard(
                          context,
                          '4',
                          localization.expenses,
                          colors.info, // Blue color
                          colors,
                          BoxDecoration(
                            color: colors.infoContainer,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          textStyles,
                        ),
                      ),
                    ),
                    Container(
                        margin: const EdgeInsetsDirectional.only(start: 8)),
                    Expanded(
                      child: _buildMetricCard(
                        context,
                        '15',
                        localization.holidays,
                        colors.primary, // Light blue color
                        colors,
                        BoxDecoration(
                          color: colors.primaryVariant,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        textStyles,
                        subtitle: localization.days,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildMetricCard(
    BuildContext context,
    String value,
    String title,
    Color numberColor,
    AppColors colors,
    BoxDecoration decoration,
    TextStyles textStyles, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: decoration,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                value,
                style: textStyles.headline.copyWith(
                  color: numberColor,
                ),
              ),
              if (subtitle != null) ...[
                Container(margin: const EdgeInsetsDirectional.only(start: 4)),
                Text(
                  subtitle,
                  style: textStyles.body2.copyWith(
                    color: colors.tertiaryText,
                  ),
                ),
              ],
            ],
          ),
          Container(margin: const EdgeInsetsDirectional.only(top: 8)),
          Text(
            title,
            style: textStyles.body2.copyWith(
              color: colors.secondaryText,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
