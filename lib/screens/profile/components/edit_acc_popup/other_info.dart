import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:flutter/material.dart';

class OtherInfo extends StatefulWidget {
  const OtherInfo({super.key});

  @override
  State<OtherInfo> createState() => _OtherInfoState();
}

class _OtherInfoState extends State<OtherInfo> {
  final TextEditingController jobTitleController = TextEditingController();
  final TextEditingController departmentController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsets.fromLTRB(10, 10, 10, 8),
      child: Column(
        children: [
          // Account title
          Container(
            width: double.infinity,
            margin: const EdgeInsets.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  localization.otherInfo,
                  style: textStyles.body.copyWith(
                    color: colors.primaryText,
                  ),
                ),
                Container(
                  height: 12,
                ),

                // Date of Birth field

                TextField(
                  controller: jobTitleController,
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    labelText: localization.dateOfBirth,
                    floatingLabelBehavior: FloatingLabelBehavior.auto,
                    labelStyle: textStyles.body2.copyWith(
                      color: colors.tertiaryText,
                    ),
                    filled: true,
                    fillColor: colors.backgroundContainer,
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: colors.strokeColor,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: colors.primary,
                      ),
                    ),
                  ),
                  style: textStyles.body2.copyWith(
                    color: colors.primaryText,
                  ),
                ),

                Container(
                  height: 8,
                ),

                // Country field
                DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    labelText: localization.country,
                    floatingLabelBehavior: FloatingLabelBehavior.auto,
                    labelStyle: textStyles.body2.copyWith(
                      color: colors.tertiaryText,
                    ),
                    filled: true,
                    fillColor: colors.backgroundContainer,
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: colors.strokeColor,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: colors.primary,
                      ),
                    ),
                  ),
                  style: textStyles.body2.copyWith(
                    color: colors.primaryText,
                  ),
                  icon: Icon(
                    Icons.keyboard_arrow_down,
                    color: colors.secondaryText,
                  ),
                  dropdownColor: colors.backgroundContainer,
                  items: [
                    DropdownMenuItem(
                      value: 'Iraq',
                      child: Text(localization.iraq),
                    ),
                    DropdownMenuItem(
                      value: 'India',
                      child: Text(localization.india),
                    ),
                  ],
                  onChanged: (value) {
                    // Handle dropdown value change
                  },
                ),

                Container(
                  height: 8,
                ),

                // Start Date field
                TextField(
                  controller: departmentController,
                  maxLines: 1,
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    labelText: localization.startDate,
                    floatingLabelBehavior: FloatingLabelBehavior.auto,
                    labelStyle: textStyles.body2.copyWith(
                      color: colors.tertiaryText,
                    ),
                    filled: true,
                    fillColor: colors.backgroundContainer,
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: colors.strokeColor,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: colors.primary,
                      ),
                    ),
                  ),
                  style: textStyles.body2.copyWith(
                    color: colors.primaryText,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
