import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:ako_basma/screens/chat/screen/components/new group/group chat/group_chat_screen.dart';

class CreateGroup extends StatefulWidget {
  final List<Map<String, dynamic>> selectedEmployees;

  const CreateGroup({
    super.key,
    required this.selectedEmployees,
  });

  @override
  State<CreateGroup> createState() => _CreateGroupState();
}

class _CreateGroupState extends State<CreateGroup> {
  // Text controller for group name input
  final TextEditingController _groupNameController = TextEditingController();

  // Image picker instance for selecting group photo
  final ImagePicker _picker = ImagePicker();

  // Selected image file for group photo
  File? _selectedGroupImage;

  // Focus node for group name text field
  final FocusNode _groupNameFocusNode = FocusNode();

  @override
  void dispose() {
    _groupNameController.dispose();
    _groupNameFocusNode.dispose();
    super.dispose();
  }

  /// Handle group photo selection from gallery
  Future<void> _selectGroupPhoto() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80, // Compress image for performance
        maxWidth: 512,
        maxHeight: 512,
      );

      if (image != null) {
        setState(() {
          _selectedGroupImage = File(image.path);
        });
      }
    } catch (e) {
      // Handle image selection error
      final localization = AppLocalizations.of(context)!;
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(localization.failedToSelectImage),
          ),
        );
      }
    }
  }

  /// Handle group creation action
  void _createGroup() {
    final localization = AppLocalizations.of(context)!;
    final groupName = _groupNameController.text.trim();

    if (groupName.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(localization.pleaseEnterGroupName),
        ),
      );
      return;
    }

    // Navigate to group chat screen with group data
    Navigator.of(context, rootNavigator: true).pushReplacement(
      MaterialPageRoute(
        builder: (context) => GroupChatScreen(
          groupName: groupName,
          groupImage: _selectedGroupImage,
          members: widget.selectedEmployees,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Back button - navigates back to add_employee screen
                  GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(8, 8, 8, 8),
                      decoration: BoxDecoration(
                        color: colors.background,
                        borderRadius: BorderRadius.circular(8),
                        // border: Border.all(
                        //   color: colors.strokeColor,
                        //   width: 1,
                        // ),
                      ),
                      child: Icon(
                        Icons.arrow_back_ios_new,
                        color: colors.primaryText,
                        size: 20,
                      ),
                    ),
                  ),

                  // Title
                  Text(
                    localization.newGroup,
                    style: textStyles.body2.copyWith(
                      color: colors.primaryText,
                    ),
                  ),

                  // Create button
                  GestureDetector(
                    onTap: _createGroup,
                    child: Text(
                      localization.create,
                      style: textStyles.textButton.copyWith(
                        color: _groupNameController.text.trim().isNotEmpty
                            ? colors.primary
                            : colors.secondaryText,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Group creation section with camera icon and group name input
            Container(
              margin: const EdgeInsetsDirectional.fromSTEB(16, 20, 16, 20),
              padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
              decoration: BoxDecoration(
                color: colors.backgroundContainer,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: colors.strokeColor,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  // Camera icon for group photo selection
                  GestureDetector(
                    onTap: _selectGroupPhoto,
                    child: Container(
                      width: 56,
                      height: 56,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _selectedGroupImage != null
                            ? Colors.transparent
                            : colors.backgroundContainer,
                      ),
                      child: _selectedGroupImage != null
                          ? ClipOval(
                              child: Image.file(
                                _selectedGroupImage!,
                                width: 56,
                                height: 56,
                                fit: BoxFit.cover,
                              ),
                            )
                          : Icon(
                              SolarIconsOutline.camera,
                              color: colors.secondaryText,
                              size: 24,
                            ),
                    ),
                  ),

                  // Group name input field
                  Expanded(
                    child: Container(
                      margin: const EdgeInsetsDirectional.only(start: 16),
                      child: TextField(
                        controller: _groupNameController,
                        focusNode: _groupNameFocusNode,
                        onChanged: (value) {
                          setState(() {
                            // Trigger rebuild to update create button color
                          });
                        },
                        style: textStyles.body2.copyWith(
                          color: colors.primaryText,
                        ),
                        decoration: InputDecoration(
                          hintText: localization.enterGroupName,
                          hintStyle: textStyles.body2.copyWith(
                            color: colors.tertiaryText,
                          ),
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Selected employees list
            Expanded(
              child: Container(
                margin: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
                child: ListView.builder(
                  itemCount: widget.selectedEmployees.length,
                  itemBuilder: (context, index) {
                    final employee = widget.selectedEmployees[index];
                    return Container(
                      margin: const EdgeInsetsDirectional.only(bottom: 16),
                      child: Row(
                        children: [
                          // Profile image
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: colors.primaryVariant,
                            ),
                            child: ClipOval(
                              child: Image.asset(
                                employee['profileImage'],
                                width: 40,
                                height: 40,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  // Fallback to initials if image fails to load
                                  return Container(
                                    color: colors.primary.withOpacity(0.1),
                                    child: Center(
                                      child: Text(
                                        employee['name'][0].toUpperCase(),
                                        style: textStyles.body2.copyWith(
                                          color: colors.primary,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),

                          // Employee name
                          Expanded(
                            child: Container(
                              margin:
                                  const EdgeInsetsDirectional.only(start: 12),
                              child: Text(
                                employee['name'],
                                style: textStyles.body2.copyWith(
                                  color: colors.primaryText,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
