import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/contact_info/contact_action_buttons.dart';
import 'package:solar_icons/solar_icons.dart';

/// Contact info popup component displayed as bottom modal sheet
/// Shows user profile picture, name, contact actions and details
class ContactInfoPopup extends StatelessWidget {
  final String userName;
  final String userImage;
  final String userEmail;
  final String userPhone;
  final VoidCallback? onCall;
  final VoidCallback? onChat;
  final VoidCallback? onEmail;

  const ContactInfoPopup({
    super.key,
    required this.userName,
    required this.userImage,
    required this.userEmail,
    required this.userPhone,
    this.onCall,
    this.onChat,
    this.onEmail,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Profile picture
          Container(
            width: double.infinity,
            height: 240,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: colors.primaryVariant,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Image.asset(
                userImage,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  // Fallback to initials if image fails to load
                  return Container(
                    color: colors.primary.withOpacity(0.1),
                    child: Center(
                      child: Text(
                        userName.isNotEmpty ? userName[0].toUpperCase() : 'U',
                        style: textStyles.headline.copyWith(
                          color: colors.primary,
                          fontSize: 48,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),

          // User name
          Container(
            width: double.infinity,
            margin: const EdgeInsetsDirectional.only(top: 12),
            child: Text(
              userName,
              style: textStyles.headline.copyWith(
                color: colors.primary,
              ),
              textAlign: TextAlign.start,
            ),
          ),

          // Action buttons (Call, Chat, Email)
          Container(
            margin: const EdgeInsetsDirectional.only(top: 16),
            child: ContactActionButtons(
              onCall: onCall,
              onChat: onChat,
              onEmail: onEmail,
            ),
          ),

          // Contact details section
          Container(
            margin: const EdgeInsetsDirectional.only(top: 10),
            child: Column(
              children: [
                // Email container
                _buildContactInfo(
                  context,
                  SolarIconsOutline.letter,
                  localization.email,
                  userEmail,
                ),

                // Phone container
                Container(
                  margin: const EdgeInsetsDirectional.only(top: 8),
                  child: _buildContactInfo(
                    context,
                    SolarIconsOutline.phoneCalling,
                    localization.phone,
                    userPhone,
                  ),
                ),
              ],
            ),
          ),

          // Close button
          Container(
            margin: const EdgeInsetsDirectional.fromSTEB(0, 10, 0, 4),
            width: double.infinity,
            height: 50,
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              style: OutlinedButton.styleFrom(
                side: BorderSide(
                  color: colors.tertiaryText,
                  width: 1,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                localization.close,
                style: textStyles.body.copyWith(
                  color: colors.secondaryText,
                ),
              ),
            ),
          ),

          // Bottom safe area
          Container(
            height: MediaQuery.of(context).padding.bottom / 2,
          ),
        ],
      ),
    );
  }
}

/// Helper method to build contact info containers
Widget _buildContactInfo(
    BuildContext context, IconData icon, String label, String value) {
  final theme = Theme.of(context);
  final colors = theme.extension<AppColors>()!;
  final textStyles = theme.extension<TextStyles>()!;

  return Container(
    width: double.infinity,
    padding: const EdgeInsetsDirectional.fromSTEB(12, 6, 12, 6),
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(8),
      border: Border.all(
        color: colors.primaryVariant,
        width: 1,
      ),
      color: colors.background,
    ),
    child: Row(
      children: [
        // Icon on the left
        Container(
          width: 20,
          height: 20,
          child: Icon(
            icon,
            size: 18,
            color: colors.secondaryText,
          ),
        ),

        // Label next to icon
        Container(
          margin: const EdgeInsetsDirectional.only(start: 12),
          child: Text(
            label,
            style: textStyles.body.copyWith(
              color: colors.secondaryText,
            ),
          ),
        ),

        // Spacer to push value to the right
        Expanded(child: Container()),

        // Value on the far right
        Text(
          value,
          style: textStyles.body.copyWith(
            color: colors.secondaryText,
          ),
        ),
      ],
    ),
  );
}
