import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/tasks/components/create_project.dart';
import 'package:ako_basma/screens/tasks/components/create_task.dart';
import 'package:ako_basma/screens/tasks/components/add_task_popup.dart';
import 'package:ako_basma/components/search bar/search_bar.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:ako_basma/screens/tasks/components/project_selection_popup.dart';
import 'package:ako_basma/screens/home/<USER>/components/task_card.dart';
import 'package:ako_basma/screens/tasks/components/task_details/task_details_popup.dart';

class Tasks extends StatefulWidget {
  const Tasks({super.key});

  @override
  State<Tasks> createState() => _TasksState();
}

class _TasksState extends State<Tasks> {
  bool _hasProject = false;
  bool _hasTasks = false;
  bool _showCreateTaskScreen =
      false; // New state to control the create task screen
  int _selectedProjectIndex = 0;
  final List<String> _projectList = [
    'Ako Basma',
    'Ako Basma',
    'Ako Basma',
    'Ako Basma',
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildAppBar(context),
        Expanded(
          child: _buildBody(context),
        ),
      ],
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    // Using a fully custom container instead of the default AppBar so that
    // we have full control over vertical alignment of all elements.
    return Container(
      color: colors.background,
      padding: const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
      child: SafeArea(
        bottom: false,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Left side – Project selector (tap to open bottom sheet) and subtitle
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Project selector (title + arrow)
                  InkWell(
                    onTap: () async {
                      showModalBottomSheet<int>(
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        useRootNavigator: true,
                        builder: (context) => ProjectSelectionPopup(
                          selectedIndex: _selectedProjectIndex,
                          projects: _projectList,
                          onSelected: (index) {
                            setState(() {
                              _selectedProjectIndex = index;
                            });
                          },
                        ),
                      );
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Flexible(
                          child: Text(
                            _hasProject
                                ? _projectList[_selectedProjectIndex]
                                : Labels.allProjects,
                            style: textStyles.headline4.copyWith(
                              color: colors.primaryText,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Container(
                            margin: const EdgeInsetsDirectional.only(start: 4)),
                        Icon(
                          Icons.keyboard_arrow_down,
                          color: colors.primary,
                          size: 20,
                        ),
                      ],
                    ),
                  ),

                  // Small subtitle – "Manage Projects / Manage Tasks"
                  Container(
                    margin: const EdgeInsetsDirectional.only(top: 10),
                    child: Text(
                      _hasProject && !_showCreateTaskScreen
                          ? 'Manage Tasks'
                          : Labels.manageProjects,
                      style: textStyles.body3.copyWith(
                        color: colors.tertiaryText,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Right side – Add button (only show when we have tasks list or create task screen)
            if (_hasTasks || _showCreateTaskScreen)
              Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  color: colors.backgroundContainer,
                  borderRadius: BorderRadius.circular(6.67),
                  border: Border.all(
                    color: colors.primaryVariant,
                    width: 1,
                  ),
                ),
                child: IconButton(
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  onPressed: () => _showAddTaskPopup(context),
                  icon: SvgPicture.asset(
                    'assets/icons/tasks_screen/add.svg',
                    colorFilter: ColorFilter.mode(
                      colors.primary,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _showCreateProjectPopup(BuildContext context) async {
    final bool? projectCreated = await showAdaptivePopup<bool>(
      context,
      (ctx, sc) => CreateProject(
        onBack: () => Navigator.pop(ctx),
        onProjectCreated: () {
          // legacy callback (not used now)
        },
      ),
      isDismissible: false,
      scrollable: true,
      contentPadding: EdgeInsets.zero,
      topRadius: 0,
      fullScreen: true,
      useRootNavigator: true,
    );

    if (projectCreated == true && mounted) {
      setState(() {
        _hasProject = true;
        _showCreateTaskScreen =
            true; // Show the create task screen instead of popup
      });
    }
  }

  Future<void> _showAddTaskPopup(BuildContext context) async {
    final bool? taskCreated = await showAdaptivePopup<bool>(
      context,
      (ctx, sc) => AddTaskPopup(
        onBack: () => Navigator.pop(ctx),
      ),
      isDismissible: false,
      scrollable: true,
      contentPadding: EdgeInsets.zero,
      topRadius: 0,
      fullScreen: true,
      useRootNavigator: true,
    );

    if (taskCreated == true && mounted) {
      setState(() {
        _hasTasks = true;
        _showCreateTaskScreen =
            false; // Hide create task screen and show tasks list
      });
    }
  }

  Widget _buildBody(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    if (!_hasProject) {
      // Show empty state for no projects
      return Container(
        color: colors.background,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Empty state icon
              SvgPicture.asset(
                'assets/images/server.svg',
                width: 64,
                height: 64,
                colorFilter: ColorFilter.mode(
                  colors.tertiaryText,
                  BlendMode.srcIn,
                ),
              ),
              Container(
                margin: const EdgeInsetsDirectional.only(top: 16),
              ),
              Text(
                Labels.noProjectFound,
                style: textStyles.headline4.copyWith(
                  color: colors.primaryText,
                ),
              ),
              const SizedBox(
                height: 14,
              ),
              Text(
                Labels.pleaseCreateAProject,
                style: textStyles.body2.copyWith(
                  color: colors.secondaryText,
                ),
              ),
              const SizedBox(
                height: 26,
              ),
              Container(
                margin: const EdgeInsetsDirectional.fromSTEB(110, 0, 110, 0),
                height: 40,
                child: ElevatedButton.icon(
                  onPressed: () => _showCreateProjectPopup(context),
                  icon: SvgPicture.asset(
                    'assets/icons/tasks_screen/add.svg',
                    width: 24,
                    height: 24,
                    colorFilter: ColorFilter.mode(
                      theme.colorScheme.onPrimary,
                      BlendMode.srcIn,
                    ),
                  ),
                  label: Text(
                    Labels.createProject,
                    style: textStyles.buttonMedium.copyWith(
                      color: theme.colorScheme.onPrimary,
                      fontSize: 16,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colors.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                    padding: const EdgeInsetsDirectional.fromSTEB(16, 8, 16, 8),
                    minimumSize: const Size.fromHeight(40),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else if (_showCreateTaskScreen) {
      // Show the CreateTask screen (not popup) - this is the screen with "Create Task" button
      return CreateTask(
        showAppBar: false,
        onTaskCreated: () {
          setState(() {
            _hasTasks = true;
            _showCreateTaskScreen = false;
          });
        },
        onBack: () {
          setState(() {
            _showCreateTaskScreen = false;
          });
        },
      );
    } else if (!_hasTasks) {
      // Show empty state for no tasks
      return Container(
        color: colors.background,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Empty state icon
              SvgPicture.asset(
                'assets/images/server.svg',
                width: 64,
                height: 64,
                colorFilter: ColorFilter.mode(
                  colors.tertiaryText,
                  BlendMode.srcIn,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'No Tasks Found',
                style: textStyles.body.copyWith(
                  color: colors.primaryText,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Please create a task to get started',
                style: textStyles.body.copyWith(
                  color: colors.secondaryText,
                ),
              ),
              const SizedBox(height: 24),
              Container(
                margin: const EdgeInsetsDirectional.fromSTEB(80, 0, 80, 0),
                child: ElevatedButton.icon(
                  onPressed: () => _showAddTaskPopup(context),
                  icon: Icon(Icons.add,
                      size: 20, color: theme.colorScheme.onPrimary),
                  label: Text(
                    'Create Task',
                    style: textStyles.buttonMedium.copyWith(
                      color: theme.colorScheme.onPrimary,
                      fontSize: 16,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colors.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                    padding: const EdgeInsetsDirectional.fromSTEB(16, 8, 16, 8),
                    minimumSize: const Size(double.infinity, 48),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      // Show tasks list which are my task card as of now
      return Container(
        color: colors.background,
        child: Column(
          children: [
            // Search bar
            Container(
              margin: const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
              child: CustomSearchBar(
                hintText: 'Search for a task',
                margin: EdgeInsets.zero,
                onChanged: (value) {
                  // TODO: Implement search functionality
                },
              ),
            ),

            // Tasks list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 16),
                itemCount: 5, // For now, show 5 sample tasks
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () {
                      showAdaptivePopup(
                        context,
                        (ctx, sc) => TaskDetailsPopup(
                          onBack: () => Navigator.pop(ctx),
                        ),
                        isDismissible: true,
                        scrollable: true,
                        contentPadding: EdgeInsets.zero,
                        topRadius: 0,
                        fullScreen: true,
                        useRootNavigator: true,
                      );
                    },
                    child: Container(
                      margin: const EdgeInsetsDirectional.only(bottom: 4),
                      child: const TaskCard(),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      );
    }
  }
}
