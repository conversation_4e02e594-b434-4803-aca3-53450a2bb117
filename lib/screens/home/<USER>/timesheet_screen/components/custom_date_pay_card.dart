import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:solar_icons/solar_icons.dart';

class CustomDatePayCard extends StatelessWidget {
  const CustomDatePayCard({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colors.strokeColor,
          width: 1,
        ),
      ),
      margin: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
      child: Padding(
        padding: const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Pay for this period row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    // Wallet icon container
                    Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                      decoration: BoxDecoration(
                        color: colors.primaryVariant,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        SolarIconsOutline.banknote,
                        color: colors.primary,
                        size: 14,
                      ),
                    ),
                    // Pay for this period label
                    Container(
                      padding: const EdgeInsetsDirectional.only(start: 12),
                      child: Text(
                        localization.payForThisPeriod,
                        style: textStyles.body2.copyWith(
                          color: colors.secondaryText,
                        ),
                      ),
                    ),
                  ],
                ),
                // Amount text
                Text(
                  'IQD 100,000',
                  style: textStyles.headline4.copyWith(
                    color: colors.primary,
                  ),
                ),
              ],
            ),
            // Total hours row
            Container(
              padding: const EdgeInsetsDirectional.only(top: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      // Clock icon container
                      Container(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                        decoration: BoxDecoration(
                          color: colors.primaryVariant,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          SolarIconsOutline.clockCircle,
                          color: colors.primary,
                          size: 14,
                        ),
                      ),
                      // Total hours label
                      Container(
                        padding: const EdgeInsetsDirectional.only(start: 12),
                        child: Text(
                          localization.totalHours,
                          style: textStyles.body2.copyWith(
                            color: colors.secondaryText,
                          ),
                        ),
                      ),
                    ],
                  ),
                  // Hours count
                  Container(
                    padding: const EdgeInsetsDirectional.only(start: 14),
                    child: Text(
                      '100',
                      style: textStyles.body2.copyWith(
                        color: colors.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
