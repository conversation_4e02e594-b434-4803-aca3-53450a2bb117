import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:solar_icons/solar_icons.dart';
// import 'package:time_picker_with_timezone/time_picker_with_timezone.dart';

class TimeEntryCard extends StatelessWidget {
  final String title;
  final String time;
  final String location;
  final VoidCallback? onEdit;
  final Function(String)? onTimeChanged;

  const TimeEntryCard({
    super.key,
    required this.title,
    required this.time,
    required this.location,
    this.onEdit,
    this.onTimeChanged,
  });

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Container(
      width: screenWidth - 32, // Fill (328px)
      height: 100,
      margin: const EdgeInsetsDirectional.only(bottom: 8),
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(width: 1, color: colors.primaryVariant),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with title and edit icon
          Container(
            padding: const EdgeInsetsDirectional.only(
              start: 12,
              top: 8,
              end: 12,
              bottom: 4, // Gap
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: textStyles.body3.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () => _showTimePicker(context),
                  child: Container(
                    child: Icon(
                      SolarIconsOutline.pen,
                      color: colors.primary,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Time
          Container(
            padding: const EdgeInsets.only(
              left: 12,
              right: 12,
              bottom: 4,
              top: 4,
            ),
            child: Text(
              time,
              style: textStyles.headline4.copyWith(
                color: colors.primaryText,
                height: 1.2,
              ),
            ),
          ),

          Container(
            margin: const EdgeInsets.only(top: 6),
          ),

          // Location
          Container(
            padding: const EdgeInsets.only(
              left: 12,
              right: 12,
              bottom: 8,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Location icon
                Container(
                  padding: const EdgeInsetsDirectional.all(4),
                  decoration: BoxDecoration(
                    color: colors.primaryVariant,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    SolarIconsOutline.mapPoint,
                    color: colors.primary,
                    size: 16,
                  ),
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(start: 6),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    location,
                    style: textStyles.body3.copyWith(
                      color: colors.secondaryText,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // code for rounded time picker

  void _showTimePicker(BuildContext context) async {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;

    final TimeOfDay? selectedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
      helpText: 'Checked Out',
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            timePickerTheme: TimePickerThemeData(
              backgroundColor: colors.background,
              hourMinuteShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              dayPeriodShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              dayPeriodColor: WidgetStateColor.resolveWith((states) =>
                  states.contains(WidgetState.selected)
                      ? colors.primary
                      : colors.primaryVariant),
              dayPeriodTextColor: WidgetStateColor.resolveWith((states) =>
                  states.contains(WidgetState.selected)
                      ? colors.primaryText
                      : colors.secondaryText),
              hourMinuteColor: WidgetStateColor.resolveWith((states) =>
                  states.contains(WidgetState.selected)
                      ? colors.primaryVariant
                      : colors.backgroundContainer),
              hourMinuteTextColor: WidgetStateColor.resolveWith((states) =>
                  states.contains(WidgetState.selected)
                      ? colors.primaryText
                      : colors.secondaryText),
              // Increased font size for hour and minute text
              hourMinuteTextStyle: const TextStyle(fontSize: 54),
              dialBackgroundColor: colors.backgroundContainer,
              dialHandColor: colors.primary,
              dialTextColor: WidgetStateColor.resolveWith((states) =>
                  states.contains(WidgetState.selected)
                      ? colors.primaryText
                      : colors.secondaryText),
              entryModeIconColor: colors.primary,
            ),
            colorScheme: ColorScheme(
              brightness: Brightness.light,
              primary: colors.primary,
              onPrimary: colors.primaryText,
              secondary: colors.secondary,
              onSecondary: colors.secondaryText,
              error: colors.error,
              onError: colors.error,
              background: colors.background,
              onBackground: colors.primaryText,
              surface: colors.primaryVariant,
              onSurface: colors.primaryText,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: colors.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (selectedTime != null && onTimeChanged != null) {
      final formattedTime = _formatTime(selectedTime);
      onTimeChanged!(formattedTime);
    }
  }

  String _formatTime(TimeOfDay time) {
    final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.period == DayPeriod.am ? 'AM' : 'PM';
    return '$hour:$minute $period';
  }
}
