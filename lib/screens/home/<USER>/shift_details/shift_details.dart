import 'dart:typed_data';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/home/<USER>/shift_details/time_entry_card.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/components/button/primary_button.dart';
import 'package:remixicon/remixicon.dart';
import 'package:ako_basma/screens/home/<USER>/shift_details/signature_popup.dart';
import 'package:ako_basma/screens/home/<USER>/shift_details/note_popup.dart';
import 'package:ako_basma/screens/home/<USER>/shift_details/top_snackbar.dart';
import 'package:solar_icons/solar_icons.dart';

class ShiftDetails extends StatefulWidget {
  final VoidCallback? onBack;
  final VoidCallback? resetToClockIn;

  const ShiftDetails({super.key, this.onBack, this.resetToClockIn});

  @override
  State<ShiftDetails> createState() => _ShiftDetailsState();
}

class _ShiftDetailsState extends State<ShiftDetails> {
  // state variables for time entry card which will be changed later
  String _clockInTime = '8:00 AM';
  String _clockOutTime = '3:30 PM';
  String _breakTime = '2:30 PM - 3:00';
  Uint8List? _signatureData;
  bool _hasSignature = false;

  // draw sign pop-up screen
  void _showSignaturePopup() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SignaturePopup(
        onCancel: () => Navigator.pop(context),
        onSave: (data) {
          setState(() {
            _signatureData = data;
            _hasSignature = true;
          });
          Navigator.pop(context);
        },
      ),
    );
  }

  // add note pop-up screen
  void _showNotePopup() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding: EdgeInsetsDirectional.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: NotePopup(
          onCancel: () => Navigator.pop(context),
          onSave: () => Navigator.pop(context),
        ),
      ),
    );
  }

  // top snackbar from top
  void _showTopSnackbar(BuildContext context) {
    final localization = AppLocalizations.of(context)!;
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => TopSnackbarWidget(
        message: localization.successfullyCompletedShift,
        onDismiss: () => overlayEntry.remove(),
      ),
    );

    overlay.insert(overlayEntry);

    // Auto remove after 3 seconds
    Future.delayed(const Duration(seconds: 3), () {
      if (overlayEntry.mounted) {
        overlayEntry.remove();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: colors.surface,
      body: SafeArea(
        child: Container(
          width: screenWidth,
          constraints: BoxConstraints(
            minHeight: mediaQuery.size.height,
          ),
          decoration: BoxDecoration(
            color: colors.background,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              // Header with back button and title
              Container(
                padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        if (widget.onBack != null) {
                          widget.onBack!();
                        } else {
                          Navigator.of(context).pop();
                        }
                      },
                      child: Container(
                        padding: const EdgeInsetsDirectional.all(4),
                        decoration: BoxDecoration(
                          color: colors.backgroundContainer,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: colors.strokeColor,
                            width: 1,
                          ),
                        ),
                        child: Icon(
                          SolarIconsOutline.altArrowLeft,
                          color: colors.primaryText,
                          size: 24,
                        ),
                      ),
                    ),
                    Container(
                      margin: const EdgeInsetsDirectional.only(start: 12),
                    ),
                    Text(
                      localization.shiftDetails,
                      style: textStyles.headline3.copyWith(
                        color: colors.secondaryText,
                      ),
                    ),
                  ],
                ),
              ),

              // Main content - scrollable
              Expanded(
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Container(
                    padding:
                        const EdgeInsetsDirectional.symmetric(horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Container(
                          margin: const EdgeInsetsDirectional.only(top: 8),
                        ),

                        // Two new containers when time is edited
                        Row(
                          children: [
                            // Draw Signature Container
                            Expanded(
                              child: GestureDetector(
                                onTap: _showSignaturePopup,
                                child: Container(
                                  height: 72,
                                  padding: const EdgeInsetsDirectional.all(8),
                                  decoration: BoxDecoration(
                                    color: colors.backgroundContainer,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: _hasSignature
                                          ? colors.primary
                                          : colors.primaryVariant,
                                      width: _hasSignature ? 2 : 1,
                                    ),
                                  ),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Stack(
                                        alignment: Alignment.center,
                                        children: [
                                          Icon(
                                            SolarIconsOutline.pen,
                                            color: colors.primary,
                                            size: 24,
                                          ),
                                          if (_hasSignature)
                                            Positioned(
                                              right: -5,
                                              top: -5,
                                              child: Container(
                                                width: 12,
                                                height: 12,
                                                decoration: BoxDecoration(
                                                  color: colors.primary,
                                                  shape: BoxShape.circle,
                                                ),
                                                child: Icon(
                                                  Icons.check,
                                                  color: colors.primaryText,
                                                  size: 8,
                                                ),
                                              ),
                                            ),
                                        ],
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        localization.drawSignature,
                                        style: textStyles.body2.copyWith(
                                          color: colors.primary,
                                          fontWeight: _hasSignature
                                              ? FontWeight.w600
                                              : FontWeight.w400,
                                          fontSize: 12,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            Container(margin: const EdgeInsets.only(left: 8)),

                            // Add Note Container
                            Expanded(
                              child: GestureDetector(
                                onTap: _showNotePopup,
                                child: Container(
                                  height: 72,
                                  padding: const EdgeInsetsDirectional.all(8),
                                  decoration: BoxDecoration(
                                    color: colors.backgroundContainer,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                        width: 1, color: colors.primaryVariant),
                                  ),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Icon(
                                        SolarIconsOutline.documentMedicine,
                                        color: colors.primary,
                                        size: 24,
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        localization.addNote,
                                        style: textStyles.body2.copyWith(
                                          color: colors.primary,
                                          fontSize: 12,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 12),
                        // Clock Out Container
                        Container(
                          // width: MediaQuery.of(context).size.width - 32,
                          decoration: BoxDecoration(
                            color:
                                colors.surface, // Transparent grey background
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Top section with transparent grey background
                              Container(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    16, 16, 16, 16),
                                width: double.infinity,
                                child: Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Expanded(
                                          child: Text(
                                            localization.totalHours,
                                            style: textStyles.body2.copyWith(
                                              color: colors.tertiaryText,
                                              fontSize: 12,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      ],
                                    ),
                                    Container(
                                      margin: const EdgeInsetsDirectional.only(
                                          top: 8),
                                    ),
                                    Text(
                                      '10:30:00',
                                      style: textStyles.headline2.copyWith(
                                        color: colors.primaryText,
                                        fontSize: 32,
                                      ),
                                    ),
                                    Container(
                                      margin: const EdgeInsets.only(top: 8),
                                    ),
                                    Text(
                                      'Saturday, 15 March 2025',
                                      style: textStyles.body2.copyWith(
                                        color: colors.secondaryText,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // Divider line
                              Container(
                                height: 1,
                                color: colors.strokeColor,
                              ),

                              // Bottom section
                              Container(
                                padding: const EdgeInsetsDirectional.all(12),
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: colors.surface,
                                  borderRadius: const BorderRadius.only(
                                    bottomLeft: Radius.circular(13),
                                    bottomRight: Radius.circular(13),
                                  ),
                                ),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Container(
                                      padding:
                                          const EdgeInsetsDirectional.all(4),
                                      decoration: BoxDecoration(
                                        color: colors.primaryVariant,
                                        shape: BoxShape.circle,
                                      ),
                                      child: Icon(
                                        SolarIconsOutline.mapPoint,
                                        color: colors.primary,
                                        size: 16,
                                      ),
                                    ),
                                    Container(
                                      margin: const EdgeInsetsDirectional.only(
                                          start: 8),
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: Text(
                                        localization.location,
                                        style: textStyles.body2.copyWith(
                                          color: colors.secondaryText,
                                          fontSize: 10,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 1,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 12),

                        // Overtime Hours
                        Container(
                          width: MediaQuery.of(context).size.width - 32,
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              12, 12, 12, 12),
                          decoration: BoxDecoration(
                            color: colors.primaryVariant,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              width: 1,
                              color: colors.primaryVariant,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    SolarIconsOutline.clockCircle,
                                    color: colors.primary,
                                    size: 20,
                                  ),
                                  Container(
                                    margin: const EdgeInsets.only(left: 6),
                                  ),
                                  Text(
                                    localization.overTime,
                                    style: textStyles.body2.copyWith(
                                      color: colors.primary,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                              Container(
                                margin: const EdgeInsets.only(top: 8, left: 2),
                                child: Text(
                                  '12 Hours for Today',
                                  style: textStyles.headline2.copyWith(
                                    color: colors.primaryText,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        Container(
                          margin: const EdgeInsets.only(top: 12),
                        ),

                        // Clock In
                        TimeEntryCard(
                          title: localization.clockIn,
                          time: _clockInTime,
                          location: localization.location,
                          onTimeChanged: (newTime) {
                            setState(() {
                              _clockInTime = newTime;
                            });
                          },
                        ),

                        Container(
                          margin: const EdgeInsets.only(top: 2),
                        ),

                        // Clock Out
                        TimeEntryCard(
                          title: localization.clockOut,
                          time: _clockOutTime,
                          location: localization.location,
                          onTimeChanged: (newTime) {
                            setState(() {
                              _clockOutTime = newTime;
                            });
                          },
                        ),

                        Container(
                          margin: const EdgeInsets.only(top: 2),
                        ),

                        // Break Time
                        TimeEntryCard(
                          title: localization.breakTime,
                          time: _breakTime,
                          location: localization.location,
                          onTimeChanged: (newTime) {
                            setState(() {
                              _breakTime = newTime;
                            });
                          },
                        ),

                        Container(
                          margin: const EdgeInsets.only(top: 2),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: colors.backgroundContainer,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                ),
                child: PrimaryButton(
                  label: localization.sendShiftChangeRequest,
                  onTap: () {
                    // Show top snackbar
                    _showTopSnackbar(context);

                    // Close the current popup
                    Navigator.of(context).pop();

                    // Reset to clock in screen if callback is provided
                    if (widget.resetToClockIn != null) {
                      widget.resetToClockIn!();
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
