import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/home/<USER>/components/all_tasks_popup.dart';

import 'package:ako_basma/screens/home/<USER>/components/task_card.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';

class MyTasks extends StatelessWidget {
  const MyTasks({super.key});

  void _showAllTasks(BuildContext context) {
    // Using root navigator to bypass bottom navigation bar for true full screen experience
    Navigator.of(context, rootNavigator: true).push(
      MaterialPageRoute(
        builder: (context) => AllTasksPopup(
          onBack: () => Navigator.of(context, rootNavigator: true).pop(),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsetsDirectional.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with horizontal padding only for text content
          Container(
            width: double.infinity,
            padding: const EdgeInsetsDirectional.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  localization.myTasks,
                  style: textStyles.headline4.copyWith(
                    color: colors.primaryText,
                  ),
                ),
                const Spacer(),
                GestureDetector(
                  onTap: () => _showAllTasks(context),
                  child: Text(
                    localization.showAll,
                    style: textStyles.body3.copyWith(
                      color: colors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 6),

          // Task card carousel - extends to screen edges
          CarouselSlider(
            options: CarouselOptions(
              height: 165,
              enlargeCenterPage: false,
              viewportFraction: 0.90, // Shows preview of next card
              enableInfiniteScroll: false,
              padEnds: false, // Removes default padding to extend to edges
            ),
            items: List.generate(
              3,
              (index) => Container(
                margin: EdgeInsetsDirectional.only(
                  start: index == 0
                      ? 16.0
                      : 0.0, // Left margin only for first card
                  end: 12.0,
                ),
                child: const TaskCard(
                  width: 332,
                  height: 165,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
