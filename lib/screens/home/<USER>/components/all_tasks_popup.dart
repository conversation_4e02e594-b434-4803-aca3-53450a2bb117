import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/home/<USER>/components/task_card.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:solar_icons/solar_icons.dart';

class AllTasksPopup extends StatelessWidget {
  final VoidCallback? onBack;

  const AllTasksPopup({
    super.key,
    this.onBack,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // App bar with back button
            Container(
              padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: onBack ??
                        () => Navigator.of(context, rootNavigator: true).pop(),
                    child: Container(
                      // Reduced padding for smaller icon container
                      padding: const EdgeInsetsDirectional.fromSTEB(6, 6, 6, 6),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        SolarIconsOutline.altArrowLeft,
                        color: colors.primaryText,
                        size: 24,
                      ),
                    ),
                  ),
                  // Reduced gap between icon and title
                  const SizedBox(width: 12),
                  Text(
                    Labels.myTasks,
                    style: textStyles.headline4.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ],
              ),
            ),

            // Tasks list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsetsDirectional.fromSTEB(16, 8, 16, 8),
                itemCount: 8, // Displaying 8 task cards
                itemBuilder: (context, index) {
                  return const TaskCard();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
