import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/screens/home/<USER>/components/news_detail_popup.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:solar_icons/solar_icons.dart';

class AllCompanyNewsScreen extends StatelessWidget {
  final VoidCallback? onBack;

  const AllCompanyNewsScreen({
    super.key,
    this.onBack,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header with back button and title
            Container(
              padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: onBack ??
                        () => Navigator.of(context, rootNavigator: true).pop(),
                    child: Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(8, 8, 8, 8),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        SolarIconsOutline.altArrowLeft,
                        color: colors.primaryText,
                        size: 24,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    Labels.news,
                    style: textStyles.headline4.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ],
              ),
            ),

            // Company News cards list - expanded to fill remaining space for full screen experience
            Expanded(
              child: ListView.builder(
                // Consistent padding with other list popups
                padding: const EdgeInsetsDirectional.fromSTEB(16, 8, 16, 16),
                itemCount: 8, // Displaying 8 news cards right now
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () => _showNewsDetail(context),
                    child: Container(
                      // Add consistent spacing between news cards
                      margin: const EdgeInsets.only(bottom: 16),
                      child: _buildNewsCard(context),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNewsCard(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    return Container(
      width: double.infinity,
      height: 220,
      // Removed individual card margin since it's now handled in ListView.builder
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: colors.strokeColor,
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Image.asset(
          'assets/images/news.png',
          fit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
        ),
      ),
    );
  }

  void _showNewsDetail(BuildContext context) {
    showAdaptivePopup(
      context,
      (ctx, sc) => NewsDetailPopup(
        onBack: () => Navigator.pop(ctx),
        title: Labels.employeeOfTheMonth,
        publishedBy: Labels.publishedBy,
        time: Labels.today0800Am,
        imagePath: 'assets/images/employee.png',
        employeeName: Labels.howardTheodore,
        description: Labels.description,
      ),
      isDismissible: false,
      scrollable: true,
      contentPadding: EdgeInsets.zero,
      topRadius: 0,
      fullScreen: true,
      useRootNavigator: true,
    );
  }
}
