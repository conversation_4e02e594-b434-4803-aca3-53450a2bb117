import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:flutter_svg/svg.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:solar_icons/solar_icons.dart';

class TaskCard extends StatelessWidget {
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? margin;

  const TaskCard({
    super.key,
    this.width,
    this.height,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Container(
      width: width ?? double.infinity,
      height: height,
      margin: margin ?? const EdgeInsetsDirectional.fromSTEB(0, 16, 0, 0),
      padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colors.primaryVariant,
          width: 1,
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  Labels.modelAnswer,
                  style: textStyles.body2.copyWith(
                    color: colors.primaryText,
                  ),
                ),
                Row(
                  children: [
                    SvgPicture.asset(
                      'assets/icons/workspace_screen/task.svg',
                      width: 20,
                      height: 20,
                      colorFilter: ColorFilter.mode(
                        colors.primary,
                        BlendMode.srcIn,
                      ),
                    ),
                    Container(
                      margin: const EdgeInsetsDirectional.only(start: 4),
                      child: Text(
                        '4',
                        style: textStyles.body3.copyWith(
                          color: colors.primary,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Container(
              margin: const EdgeInsetsDirectional.only(top: 8),
              child: Row(
                children: [
                  // First button
                  Container(
                    margin: const EdgeInsetsDirectional.only(end: 10),
                    child: OutlinedButton(
                      onPressed: () {},
                      style: OutlinedButton.styleFrom(
                        foregroundColor: colors.secondaryText,
                        backgroundColor: colors.background,
                        side: BorderSide(width: 1, color: colors.strokeColor),
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(8, 5, 8, 5),
                        minimumSize: const Size(0, 20),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      child: Text(
                        '#UI007',
                        style: textStyles.body3.copyWith(
                          color: colors.secondaryText,
                        ),
                      ),
                    ),
                  ),

                  // Second button
                  Container(
                    margin: const EdgeInsetsDirectional.only(end: 10),
                    child: OutlinedButton(
                      onPressed: () {},
                      style: OutlinedButton.styleFrom(
                        foregroundColor: colors.success,
                        backgroundColor: colors.successContainer,
                        side: BorderSide.none,
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(8, 5, 8, 5),
                        minimumSize: const Size(0, 20),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      child: Text(
                        Labels.design,
                        style: textStyles.body3.copyWith(
                          color: colors.success,
                        ),
                      ),
                    ),
                  ),

                  // Third button
                  Container(
                    child: OutlinedButton(
                      onPressed: () {},
                      style: OutlinedButton.styleFrom(
                        foregroundColor: colors.warning,
                        backgroundColor: colors.warningContainer,
                        side: BorderSide.none,
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(8, 5, 8, 5),
                        minimumSize: const Size(0, 20),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      child: Text(
                        Labels.backlog,
                        style: textStyles.body3.copyWith(
                          color: colors.warning,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Bottom row with profiles and icons
            Container(
              margin: const EdgeInsetsDirectional.only(top: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Profile avatars with +5 indicator
                  SizedBox(
                    width: 120,
                    child: Stack(
                      children: [
                        // First profile
                        Container(
                          width: 28,
                          height: 28,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: colors.backgroundContainer,
                              width: 2,
                            ),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(14),
                            child: Image.asset(
                              'assets/images/person.png',
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),

                        // Second profile
                        PositionedDirectional(
                          start: 18,
                          child: Container(
                            width: 28,
                            height: 28,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: colors.backgroundContainer,
                                width: 2,
                              ),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(14),
                              child: Image.asset(
                                'assets/images/person.png',
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                        ),

                        // +5 indicator
                        PositionedDirectional(
                          start: 36,
                          child: Container(
                            width: 28,
                            height: 28,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: colors.backgroundContainer,
                              border: Border.all(
                                color: colors.primaryVariant,
                                width: 1,
                              ),
                            ),
                            child: Center(
                              child: Text(
                                '+5',
                                style: textStyles.body2.copyWith(
                                  color: colors.secondaryText,
                                ),
                              ),
                            ),
                          ),
                        ),

                        // Dotted add button
                        PositionedDirectional(
                          start: 72,
                          child: DottedBorder(
                            borderType: BorderType.Circle,
                            color: colors.strokeColor,
                            strokeWidth: 1,
                            dashPattern: const [3, 2],
                            padding: EdgeInsets.zero,
                            child: Container(
                              width: 28,
                              height: 28,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: colors.background,
                              ),
                              child: Icon(
                                SolarIconsOutline.addCircle,
                                size: 16,
                                color: colors.tertiaryText,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Icons
                  Row(
                    children: [
                      // Document icon
                      Container(
                        margin: const EdgeInsetsDirectional.only(end: 16),
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              'assets/icons/workspace_screen/file.svg',
                              width: 17.92,
                              height: 17.92,
                              colorFilter: ColorFilter.mode(
                                colors.secondaryText,
                                BlendMode.srcIn,
                              ),
                            ),
                            Container(
                              margin:
                                  const EdgeInsetsDirectional.only(start: 4),
                              child: Text(
                                '2',
                                style: textStyles.body2.copyWith(
                                  color: colors.secondaryText,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Comment icon
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                2, 2, 2, 2),
                            child: SvgPicture.asset(
                              'assets/icons/workspace_screen/message.svg',
                              width: 17.92,
                              height: 17.92,
                              colorFilter: ColorFilter.mode(
                                colors.warning,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                          Container(
                            margin: const EdgeInsetsDirectional.only(start: 4),
                            child: Text(
                              '3',
                              style: textStyles.body2.copyWith(
                                color: colors.warning,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
