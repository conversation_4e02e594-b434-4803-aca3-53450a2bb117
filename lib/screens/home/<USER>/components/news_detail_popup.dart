import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:solar_icons/solar_icons.dart';

class NewsDetailPopup extends StatelessWidget {
  final VoidCallback? onBack;
  final String title;
  final String publishedBy;
  final String time;
  final String imagePath;
  final String employeeName;
  final String description;

  const NewsDetailPopup({
    super.key,
    this.onBack,
    required this.title,
    required this.publishedBy,
    required this.time,
    required this.imagePath,
    required this.employeeName,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with back button and title
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: onBack ?? () => Navigator.pop(context),
                    child: Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(8, 8, 8, 8),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        SolarIconsOutline.altArrowLeft,
                        color: colors.primaryText,
                        size: 24,
                      ),
                    ),
                  ),
                  Container(
                    margin: const EdgeInsetsDirectional.only(start: 12),
                    child: Text(
                      title,
                      style: textStyles.headline4.copyWith(
                        color: colors.secondaryText,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // News content
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(16, 8, 16, 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title

                      Text(
                        title,
                        style: textStyles.headline4.copyWith(
                          color: colors.primaryText,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsetsDirectional.only(top: 8),
                      ),

                      // Published info
                      Text(
                        publishedBy,
                        style: textStyles.body2.copyWith(
                          color: colors.tertiaryText,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsetsDirectional.only(top: 8),
                      ),

                      // Time
                      Text(
                        time,
                        style: textStyles.body2.copyWith(
                          color: colors.tertiaryText,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsetsDirectional.only(top: 16),
                      ),

                      // Empoloyee image
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: colors.backgroundContainer,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: colors.strokeColor,
                            width: 1,
                          ),
                        ),
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            16, 12, 16, 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(12),
                              child: Image.asset(
                                imagePath,
                                fit: BoxFit.cover,
                                width: double.infinity,
                                height: 400,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              employeeName,
                              style: textStyles.headline4.copyWith(
                                color: colors.primaryText,
                              ),
                            ),
                            // Employee description
                            const SizedBox(height: 16),
                            Text(
                              description,
                              style: textStyles.body.copyWith(
                                color: colors.secondaryText,
                                height: 1.3,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsetsDirectional.only(top: 12),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
