import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/home/<USER>/components/company_news_popup.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:carousel_slider/carousel_slider.dart';

class CompanyNews extends StatefulWidget {
  const CompanyNews({super.key});

  @override
  State<CompanyNews> createState() => _CompanyNewsState();
}

class _CompanyNewsState extends State<CompanyNews> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    // 4 company news images
    final items = List<String>.filled(4, 'assets/images/news.png');

    return Container(
      padding: const EdgeInsetsDirectional.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with horizontal padding only for text content
          Container(
            width: double.infinity,
            padding: const EdgeInsetsDirectional.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  localization.companyNews,
                  style: textStyles.headline4.copyWith(
                    color: colors.primaryText,
                  ),
                ),
                const Spacer(),
                GestureDetector(
                  onTap: () => _showAllCompanyNews(context),
                  child: Text(
                    localization.showAll,
                    style: textStyles.body3.copyWith(
                      color: colors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          // Company news carousel - extends to screen edges
          CarouselSlider(
            items: items.asMap().entries.map((entry) {
              int index = entry.key;
              String path = entry.value;
              return GestureDetector(
                onTap: () => _showAllCompanyNews(context),
                child: Container(
                  margin: EdgeInsetsDirectional.only(
                    start:
                        index == 0 ? 16.0 : 4.0, // Left margin for first card
                    end: index == items.length - 1
                        ? 16.0
                        : 4.0, // Right margin for last card
                    top: 4.0,
                    bottom: 4.0,
                  ),
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: colors.primaryVariant,
                      width: 1,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Image.asset(
                      path,
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                    ),
                  ),
                ),
              );
            }).toList(),
            options: CarouselOptions(
              height: 220,
              viewportFraction: 0.85, // Show more of next card
              enlargeCenterPage: false, // Disable to maintain consistent sizing
              enableInfiniteScroll: true,
              autoPlay: true,
              autoPlayInterval: const Duration(seconds: 3),
              padEnds: false, // Removes default padding to extend to edges
            ),
          ),
        ],
      ),
    );
  }

  void _showAllCompanyNews(BuildContext context) {
    // Using root navigator to bypass bottom navigation bar for true full screen experience
    Navigator.of(context, rootNavigator: true).push(
      MaterialPageRoute(
        builder: (context) => AllCompanyNewsScreen(
          onBack: () => Navigator.of(context, rootNavigator: true).pop(),
        ),
      ),
    );
  }
}
