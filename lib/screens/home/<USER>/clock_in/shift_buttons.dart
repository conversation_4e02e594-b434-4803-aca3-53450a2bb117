import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';

class ShiftButtons extends StatefulWidget {
  final VoidCallback? onEndShiftPressed;

  const ShiftButtons({
    super.key,
    this.onEndShiftPressed,
  });

  @override
  State<ShiftButtons> createState() => _ShiftButtonsState();
}

class _ShiftButtonsState extends State<ShiftButtons> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final screenWidth = MediaQuery.of(context).size.width;
    final containerWidth =
        screenWidth - 48; // Full width minus margins (24px on each side)

    return Container(
      width: containerWidth,
      padding: const EdgeInsetsDirectional.all(8),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            margin: const EdgeInsetsDirectional.only(bottom: 2),
            width: double.infinity,
            height: 44,
            child: OutlinedButton(
              onPressed: () {},
              style: OutlinedButton.styleFrom(
                foregroundColor: colors.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                side: BorderSide(color: colors.primary, width: 1),
                padding: const EdgeInsetsDirectional.fromSTEB(16, 8, 16, 8),
              ),
              child: Text(
                Labels.startBreak,
                style: textStyles.button.copyWith(
                  color: colors.primary,
                ),
              ),
            ),
          ),
          Container(
            margin: const EdgeInsetsDirectional.only(top: 8),
          ),
          Container(
            width: double.infinity,
            height: 44,
            child: ElevatedButton(
              onPressed: widget.onEndShiftPressed,
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                backgroundColor: colors.error,
                foregroundColor: colors.primaryText,
                padding: const EdgeInsetsDirectional.fromSTEB(16, 8, 16, 8),
              ),
              child: Text(
                Labels.endShift,
                style: textStyles.button.copyWith(
                  color: theme.colorScheme.onPrimary,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
