import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/home/<USER>/clock_in/clock_in.dart';
import 'package:ako_basma/screens/home/<USER>/clock_in/clock_in_details.dart';
import 'package:ako_basma/screens/home/<USER>/clock_out/clock_out.dart';

import 'package:ako_basma/screens/home/<USER>/greeting.dart';
import 'package:ako_basma/screens/home/<USER>/location.dart';
import 'package:ako_basma/screens/home/<USER>/shift_details/shift_details.dart';
import 'package:ako_basma/components/switch/switch_button_group.dart';
import 'package:ako_basma/screens/home/<USER>/workspace.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:ako_basma/components/FAB/floating_action_button.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool _showClockInDetails = false;
  bool _showClockOut = false;
  bool _showWorkspace = false;

  void toggleClockInDetails(bool show) {
    setState(() {
      _showClockInDetails = show;
      _showClockOut = false;
    });
  }

  void toggleClockOut(bool show) {
    setState(() {
      _showClockInDetails = false;
      _showClockOut = show;
    });
  }

  void toggleWorkspace(bool show) {
    setState(() {
      _showWorkspace = show;
      _showClockInDetails = false;
      _showClockOut = false;
    });
  }

  void _onAIPressed() {
    // Navigate to AI chat screen using GoRouter
    context.push('/ai-chat');
  }

  void _showShiftDetailsPopup(BuildContext context) {
    showAdaptivePopup(
      context,
      (ctx, sc) => ShiftDetails(
        onBack: () {
          Navigator.of(ctx).pop();
        },
        resetToClockIn: () {
          // Reset to clock in screen
          setState(() {
            _showClockInDetails = false;
            _showClockOut = false;
            _showWorkspace = false;
          });
        },
      ),
      isDismissible: false,
      scrollable: true,
      contentPadding: EdgeInsets.zero,
      topRadius: 0,
      fullScreen: true,
      useRootNavigator: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final localization = AppLocalizations.of(context)!;

    return Stack(
      children: [
        // Main content wrapped in SingleChildScrollView
        SingleChildScrollView(
          child: Column(
            children: [
              const Greeting(),
              Container(
                margin: const EdgeInsetsDirectional.only(top: 10),
                child: SwitchButtonGroup(
                  labels: [localization.timeClock, localization.workspace],
                  onTap: (index) {
                    setState(() {
                      final isTimeClockSelected = index == 0;
                      _showWorkspace = !isTimeClockSelected;
                      if (isTimeClockSelected) {
                        _showClockInDetails = false;
                        _showClockOut = false;
                      }
                    });
                  },
                ),
              ),
              if (_showWorkspace)
                const Workspace()
              else if (_showClockInDetails)
                ClockInDetailsScreen(
                  onBack: () => toggleClockInDetails(false),
                  onEndShift: () => toggleClockOut(true),
                )
              else if (_showClockOut)
                Column(
                  children: [
                    const Location(),
                    ClockOut(
                      onClockOutPressed: () {
                        _showShiftDetailsPopup(context);
                      },
                    ),
                  ],
                )
              else
                Column(
                  children: [
                    const Location(),
                    ClockIn(
                      onClockInPressed: () => toggleClockInDetails(true),
                    ),
                  ],
                ),
            ],
          ),
        ),

        // AI Floating Action Button with gradient background
        FABConfigurations.homeScreenFAB(
          onAITap: _onAIPressed,
          colors: colors,
        ),
      ],
    );
  }
}
