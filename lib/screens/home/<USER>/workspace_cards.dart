import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';

Widget buildWorkspaceCard(
  BuildContext context, {
  required dynamic icon,
  required String label,
  required VoidCallback onTap,
}) {
  final theme = Theme.of(context);
  final colors = theme.extension<AppColors>()!;
  final textStyles = theme.extension<TextStyles>()!;
  return Expanded(
    child: GestureDetector(
      onTap: onTap,
      child: Container(
        width: 160,
        height: 70,
        margin: const EdgeInsetsDirectional.fromSTEB(8, 8, 8, 8),
        decoration: BoxDecoration(
          color: colors.backgroundContainer,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: colors.primaryVariant,
            width: 1,
          ),
        ),
        padding: const EdgeInsetsDirectional.all(8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            icon is IconData
                ? Icon(
                    icon,
                    size: 24,
                    color: colors.primary,
                  )
                : icon is Widget
                    ? icon
                    : Icon(
                        Icons.error,
                        size: 24,
                        color: colors.primary,
                      ),
            Container(
              margin: const EdgeInsets.only(top: 8),
              child: Text(
                label,
                textAlign: TextAlign.center,
                style: textStyles.body3.copyWith(
                  color: colors.primary,
                ),
              ),
            ),
          ],
        ),
      ),
    ),
  );
}
