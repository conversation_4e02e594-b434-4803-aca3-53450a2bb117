import 'dart:io';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/image/image_service.dart';
import 'package:ako_basma/util/document/document_service.dart';
import 'package:ako_basma/screens/home/<USER>/AI/ai_chat_header.dart';
import 'package:ako_basma/screens/home/<USER>/AI/ai_greeting.dart';
import 'package:ako_basma/screens/home/<USER>/AI/ai_welcome_container.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/chat_messages_list.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/chat_input_field.dart';
import 'package:ako_basma/screens/chat/screen/components/attachment%20menu/attachment_menu.dart';
import 'package:ako_basma/screens/chat/screen/components/attachment%20menu/contact_selection_modal.dart';
import 'package:ako_basma/screens/chat/screen/components/attachment%20menu/location_sharing_modal.dart';

class AIChat extends StatefulWidget {
  const AIChat({
    super.key,
  });

  @override
  State<AIChat> createState() => _AIChatState();
}

class _AIChatState extends State<AIChat> {
  bool _showAttachmentMenu = false;

  /// Toggles the attachment menu visibility
  void _toggleAttachmentMenu() {
    setState(() {
      _showAttachmentMenu = !_showAttachmentMenu;
    });
  }

  /// Handles camera functionality - opens camera to capture photo
  Future<void> _handleCameraPress() async {
    try {
      _toggleAttachmentMenu(); // Close attachment menu first

      // Use the reusable image service to capture photo
      final File? photo = await ImageService.captureFromCamera();

      if (photo != null) {
        // Handle the captured photo
        // TODO: Process and send the image in chat
        // implement image sending logic here

        // Show success feedback using image service
        if (mounted) {
          ImageService.showSuccessMessage(
              context, 'Photo captured successfully!');
        }
      }
    } catch (e) {
      // Handle any errors during camera operation using image service
      if (mounted) {
        ImageService.showErrorMessage(
            context, 'Failed to capture photo: ${e.toString()}');
      }
    }
  }

  /// Handles gallery functionality
  Future<void> _handleGalleryPress() async {
    try {
      _toggleAttachmentMenu(); // Close attachment menu first

      // Use the reusable image service to select from gallery
      final File? image = await ImageService.selectFromGallery();

      if (image != null) {
        // Handle the selected image
        // TODO: Process and send the image in chat

        // Show success feedback using image service
        if (mounted) {
          ImageService.showSuccessMessage(
              context, 'Image selected successfully!');
        }
      }
    } catch (e) {
      // Handle any errors during gallery operation using image service
      if (mounted) {
        ImageService.showErrorMessage(
            context, 'Failed to select image: ${e.toString()}');
      }
    }
  }

  /// Handles document functionality - opens file picker to select document
  Future<void> _handleDocumentPress() async {
    try {
      _toggleAttachmentMenu(); // Close attachment menu first

      // Use the reusable document service to select document
      final File? document = await DocumentService.selectDocument();

      if (document != null) {
        // Handle the selected document
        final fileName = DocumentService.getFileName(document.path);
        final fileSize = DocumentService.getFileSize(document);
        print('Document selected: ${document.path}');
        print('File name: $fileName, Size: $fileSize');
        // TODO: Process and send the document in chat

        // Show success feedback using document service
        if (mounted) {
          DocumentService.showSuccessMessage(
              context, 'Document "$fileName" selected successfully!');
        }
      }
    } catch (e) {
      // Handle any errors during document operation using document service
      if (mounted) {
        DocumentService.showErrorMessage(
            context, 'Failed to select document: ${e.toString()}');
      }
    }
  }

  /// Handles contact selection functionality - opens contact selection popup
  void _handleContactPress() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) => ContactSelectionModal(
        onContactsSelected: (selectedContacts) {
          // Handle selected contacts
          if (selectedContacts.isNotEmpty) {
            print(
                'Selected contacts: ${selectedContacts.map((c) => c.name).join(', ')}');
            // TODO: Implement contact sharing logic
          }
        },
      ),
    );
  }

  /// Handles location sharing functionality - opens location sharing modal
  void _handleLocationPress() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) => LocationSharingModal(
        onLocationSelected: (locationType, locationData) {
          // Handle location sharing
          print('Location type: $locationType');
          print('Location data: $locationData');
          // TODO: Implement location sharing logic
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;

    return GestureDetector(
      // Close attachment menu when tapping outside
      onTap: () {
        if (_showAttachmentMenu) {
          _toggleAttachmentMenu();
        }
      },
      child: Scaffold(
        backgroundColor: colors.background,
        body: Stack(
          children: [
            // Main chat layout
            Column(
              children: [
                // Custom AI Chat Header
                AIChatHeader(
                  onBackPressed: () => context.pop(),
                ),

                // AI Content with messages in scrollable area
                const Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Greeting from AI
                        AIGreeting(),

                        // Welcome information for user
                        AIWelcomeContainer(),

                        // Chat messages list
                        ChatMessagesList(),
                      ],
                    ),
                  ),
                ),

                // Input field at bottom
                ChatInputField(
                  onSendMessage: (message) {
                    // Handle sending message to AI
                    // TODO: Implement AI message processing logic
                    print('AI Message sent: $message');
                  },
                  onAttachmentPressed: _toggleAttachmentMenu,
                  onVoicePressed: () {
                    // Handle voice message for AI
                    print('AI Voice message pressed');
                  },
                ),
              ],
            ),

            // Attachment menu overlay
            if (_showAttachmentMenu)
              PositionedDirectional(
                bottom: 100, // Position above the input field
                start: MediaQuery.of(context).size.width *
                    0.02, // Center with same margin as input field
                end: MediaQuery.of(context).size.width * 0.02,
                child: AttachmentMenu(
                  onCameraPressed:
                      _handleCameraPress, // Use the new camera handler
                  onRecordPressed: () {
                    // Handle record action
                    _toggleAttachmentMenu();
                    print('Record pressed');
                  },
                  onContactPressed: () {
                    // Handle contact action
                    _toggleAttachmentMenu();
                    _handleContactPress();
                  },
                  onGalleryPressed:
                      _handleGalleryPress, // Use the new gallery handler
                  onLocationPressed: () {
                    // Handle location action
                    _toggleAttachmentMenu();
                    _handleLocationPress();
                  },
                  onDocumentPressed:
                      _handleDocumentPress, // Use the new document handler
                ),
              ),
          ],
        ),
      ),
    );
  }
}
