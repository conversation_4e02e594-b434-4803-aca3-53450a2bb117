import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/home/<USER>/notifications.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class Greeting extends StatefulWidget {
  const Greeting({super.key});

  @override
  State<Greeting> createState() => _GreetingState();
}

class _GreetingState extends State<Greeting> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      height: 56,
      width: MediaQuery.of(context).size.width,
      padding: const EdgeInsetsDirectional.symmetric(horizontal: 16),
      margin: EdgeInsetsDirectional.only(
        top: MediaQuery.of(context).padding.top + 8,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              DecoratedBox(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(100),
                    border: Border.all(
                      width: 1,
                      color: colors.tertiaryText.withOpacity(0.5 * 0.37),
                    )),
                child: Container(
                  margin: const EdgeInsetsDirectional.all(4),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(100),
                      border: Border.all(
                        width: 1,
                        color: colors.tertiaryText,
                      )),
                  child: Container(
                      height: 40,
                      width: 40,
                      margin: const EdgeInsetsDirectional.all(3),
                      foregroundDecoration: BoxDecoration(
                        border: Border.all(
                          color: colors.tertiaryText,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(17),
                        child: const ImageContainer(
                          url: null,
                          placeholderAsset: 'assets/images/person.png',
                          height: 30,
                          width: 30,
                          placeholderFit: BoxFit.cover,
                          fit: BoxFit.cover,
                        ),
                      )),
                ),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    localization.welcome,
                    style: textStyles.body3.copyWith(color: colors.primaryText),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    localization.userName,
                    style: textStyles.body3.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ],
              ),
            ],
          ),
          GestureDetector(
            onTap: () {
              showAdaptivePopup(
                context,
                (ctx, sc) => const Notifications(),
                isDismissible: false,
                scrollable: true,
                contentPadding: EdgeInsets.zero,
                topRadius: 0,
                fullScreen: true,
                useRootNavigator: true,
              );
            },
            child: Container(
              height: 40,
              width: 40,
              decoration: BoxDecoration(
                border: Border.all(
                  color: colors.strokeColor,
                  width: 1,
                ),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: SvgPicture.asset(
                  'assets/icons/home_screen/notification.svg',
                  width: 20,
                  height: 20,
                  colorFilter: ColorFilter.mode(
                    colors.secondaryText,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
