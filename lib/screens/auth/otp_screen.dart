import 'package:ako_basma/constants/assets.dart';
import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/auth/otp_inputs.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../components/button/primary_button.dart';

class OTPScreen extends StatefulWidget {
  const OTPScreen({super.key});

  @override
  State<OTPScreen> createState() => _OTPScreenState();
}

class _OTPScreenState extends State<OTPScreen> {
  final TextEditingController _otpController = TextEditingController();

  @override
  void dispose() {
    _otpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            return SingleChildScrollView(
              padding: EdgeInsetsDirectional.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
              ),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: constraints.maxHeight,
                ),
                child: IntrinsicHeight(
                  child: Column(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              24, 0, 24, 0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0, 80, 0, 20),
                                child: Column(
                                  children: [
                                    Image.asset(
                                      Assets.logoImage,
                                      height: 100,
                                      width: 94.74,
                                    ),
                                    Container(
                                      margin: const EdgeInsetsDirectional.only(
                                          top: 16),
                                    ),
                                    Container(
                                      width: 200.57,
                                      height: 50,
                                      child: Text(
                                        Labels.appName,
                                        style: textStyles.headline.copyWith(
                                          color: colors.primary,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 36,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0, 40, 0, 8),
                                child: Container(
                                  width: 290,
                                  height: 27,
                                  child: Text(
                                    Labels.checkYourMessages,
                                    style: theme.textTheme.titleLarge?.copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                              Container(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    16, 8, 16, 8),
                                child: Container(
                                  width: 290,
                                  height: 42,
                                  child: Text(
                                    Labels.verificationCode,
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      color: theme.colorScheme.onSurface,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                              Container(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    6, 32, 6, 32),
                                child: OtpInputs(
                                  controller: _otpController,
                                ),
                              ),
                              Container(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0, 1, 0, 1),
                                width: 328,
                                height: 40,
                                child: TextButton(
                                  onPressed: () {
                                    // Implement resend functionality
                                  },
                                  child: Text(
                                    Labels.resendCode,
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      color: colors.primary,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      // Container(
                      //   margin: const EdgeInsetsDirectional.only(bottom: 20),
                      // ),
                      Container(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(24, 16, 24, 8),
                        width: MediaQuery.of(context).size.width,
                        child: PrimaryButton.async(
                          label: Labels.next,
                          textStyle: textStyles.buttonMedium.copyWith(
                            color: theme.colorScheme.onPrimary,
                          ),
                          expand: true,
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              24, 12, 24, 12),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: theme.colorScheme.primary,
                            foregroundColor: theme.colorScheme.onPrimary,
                            minimumSize: const Size.fromHeight(56),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                          onPressed: () async {
                            context.go('/home');
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
