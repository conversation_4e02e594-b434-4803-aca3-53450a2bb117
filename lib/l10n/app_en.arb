{"@@locale": "en", "appName": "<PERSON><PERSON>", "@appName": {"description": "The name of the application"}, "next": "Next", "@next": {"description": "Next button text"}, "close": "Close", "@close": {"description": "Close button text"}, "back": "Back", "@back": {"description": "Back button text"}, "loading": "Loading...", "@loading": {"description": "Loading indicator text"}, "save": "Save", "@save": {"description": "Save button"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "edit": "Edit", "@edit": {"description": "Edit button text"}, "delete": "Delete", "@delete": {"description": "Delete button text"}, "confirm": "Confirm", "@confirm": {"description": "Confirm button text"}, "success": "Success", "@success": {"description": "Success message"}, "error": "Error", "@error": {"description": "Error message"}, "noData": "No data available", "@noData": {"description": "No data message"}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "send": "Send", "@send": {"description": "Send button"}, "create": "Create", "@create": {"description": "Create button text"}, "reject": "Reject", "@reject": {"description": "Reject button"}, "approve": "Approve", "@approve": {"description": "Approve button"}, "confirmed": "Confirmed", "@confirmed": {"description": "Confirmed status"}, "pending": "Pending", "@pending": {"description": "Pending status"}, "paid": "Paid", "@paid": {"description": "Payment status"}, "search": "Search", "@search": {"description": "Search placeholder text"}, "searching": "Searching...", "@searching": {"description": "Search placeholder text"}, "filter": "Filter", "@filter": {"description": "Filter option"}, "today": "Today", "@today": {"description": "Today filter option"}, "welcome": "Welcome", "@welcome": {"description": "Welcome greeting text"}, "home": "Home", "@home": {"description": "Home tab label"}, "chat": "Cha<PERSON>", "@chat": {"description": "Chat tab label"}, "tasks": "Tasks", "@tasks": {"description": "Tasks tab label"}, "profile": "Profile", "@profile": {"description": "Profile tab label"}, "timeClock": "Time Clock", "@timeClock": {"description": "Time Clock tab"}, "workspace": "Workspace", "@workspace": {"description": "Workspace tab"}, "checkYourMessages": "Check Your Messages", "@checkYourMessages": {"description": "OTP screen title"}, "verificationCode": "We've sent a verification code to your phone number", "@verificationCode": {"description": "Verification code instruction"}, "resendCode": "Resend Code", "@resendCode": {"description": "Resend verification code button"}, "name": "Name", "@name": {"description": "Name field"}, "userName": "<PERSON><PERSON>", "@userName": {"description": "Actual user name"}, "userLocation": "10 Blackstone Street, London, UK", "@userLocation": {"description": "User's current work location"}, "currentClockInTime": "10:30:00", "@currentClockInTime": {"description": "Current clock in time"}, "role": "Role", "@role": {"description": "Role field"}, "userRole": "UI/UX Designer", "@userRole": {"description": "Actual user role"}, "designation": "Design Team", "@designation": {"description": "Designation field"}, "dob": "Date of Birth", "@dob": {"description": "Date of birth"}, "startDate": "Start Date", "@startDate": {"description": "Start date"}, "editAccountInfo": "Edit Account Info", "@editAccountInfo": {"description": "Edit account info button"}, "phoneNumber": "Phone Number", "@phoneNumber": {"description": "Phone number field"}, "emailAddress": "Email Address", "@emailAddress": {"description": "Email address field"}, "email": "Email", "@email": {"description": "Email label"}, "phone": "Phone", "@phone": {"description": "Phone label"}, "accountInfo": "Account Info", "@accountInfo": {"description": "Account info section"}, "otherInfo": "Other Info", "@otherInfo": {"description": "Other info section"}, "notEditable": "Not Editable", "@notEditable": {"description": "Not editable message"}, "department": "Department", "@department": {"description": "Department field label"}, "settings": "Settings", "@settings": {"description": "Settings title"}, "language": "Language", "@language": {"description": "Language setting"}, "notifications": "Notifications", "@notifications": {"description": "Notifications setting"}, "notification": "Notification", "@notification": {"description": "Notification title"}, "logout": "Logout", "@logout": {"description": "Logout button text"}, "themeMode": "Theme Mode", "@themeMode": {"description": "Theme mode setting"}, "reportAProblem": "Report A Problem", "@reportAProblem": {"description": "Report problem option"}, "chatWithHR": "Chat With HR", "@chatWithHR": {"description": "Chat with HR option"}, "aboutUs": "About Us", "@aboutUs": {"description": "About us option"}, "termsOfUse": "Terms of Use", "@termsOfUse": {"description": "Terms of use option"}, "about": "About", "@about": {"description": "About section"}, "newGroup": "New Group", "@newGroup": {"description": "New group option"}, "newChannel": "New Channel", "@newChannel": {"description": "New channel option"}, "ai": "AI", "@ai": {"description": "AI label"}, "requestToChatWithHR": "Request to Chat with HR", "@requestToChatWithHR": {"description": "HR chat request title"}, "hrGreeting": "Hello! How can I help you today?", "@hrGreeting": {"description": "HR greeting message"}, "hrSampleMessage": "I wanted to discuss my work schedule.", "@hrSampleMessage": {"description": "HR sample message"}, "writeAMessage": "Write a message...", "@writeAMessage": {"description": "Message input placeholder"}, "hrName": "HR Manager", "@hrName": {"description": "HR manager name"}, "hrLastSeen": "Last seen 2 hours ago", "@hrLastSeen": {"description": "HR last seen status"}, "hrApprovalMessage": "Your request has been approved.", "@hrApprovalMessage": {"description": "HR approval message"}, "youCreatedGroup": "You created this group", "@youCreatedGroup": {"description": "Group creation message"}, "call": "Call", "@call": {"description": "Call action"}, "groupName": "Group Name", "@groupName": {"description": "Group name field"}, "enterGroupName": "Enter group name", "@enterGroupName": {"description": "Group name placeholder"}, "groupDescription": "Group Description", "@groupDescription": {"description": "Group description field"}, "enterGroupDescription": "Enter group description", "@enterGroupDescription": {"description": "Group description placeholder"}, "camera": "Camera", "@camera": {"description": "Camera option"}, "record": "Record", "@record": {"description": "Record option"}, "contact": "Contact", "@contact": {"description": "Contact option"}, "gallery": "Gallery", "@gallery": {"description": "Gallery option"}, "myLocation": "My Location", "@myLocation": {"description": "Location option"}, "document": "Document", "@document": {"description": "Document option"}, "noContactsFound": "No contacts found", "@noContactsFound": {"description": "Empty state message"}, "createTask": "Create Task", "@createTask": {"description": "Create task button"}, "createProject": "Create Project", "@createProject": {"description": "Create project button"}, "allProjects": "All Projects", "@allProjects": {"description": "All projects filter"}, "manageProjects": "Manage Projects", "@manageProjects": {"description": "Manage projects option"}, "noTasksFound": "No Tasks Found", "@noTasksFound": {"description": "No tasks message"}, "pleaseCreateATask": "Please create a task", "@pleaseCreateATask": {"description": "Create task prompt"}, "noProjectFound": "No Project Found", "@noProjectFound": {"description": "No project message"}, "pleaseCreateAProject": "Please create a project", "@pleaseCreateAProject": {"description": "Create project prompt"}, "comments": "Comments", "@comments": {"description": "Comments section title"}, "canIGetMoreInfo": "Can I get more info?", "@canIGetMoreInfo": {"description": "Request info comment"}, "statusUpdate": "Status Update", "@statusUpdate": {"description": "Status update comment"}, "taskCompleted": "Task Completed", "@taskCompleted": {"description": "Task completed comment"}, "needHelp": "Need Help", "@needHelp": {"description": "Need help comment"}, "approveTask": "Approve Task", "@approveTask": {"description": "Approve task comment"}, "addComment": "Add Comment", "@addComment": {"description": "Add comment button"}, "taskDetails": "Task Details", "@taskDetails": {"description": "Task details header"}, "title": "Title", "@title": {"description": "Title field label"}, "description": "Description", "@description": {"description": "Description label"}, "assignee": "Assignee", "@assignee": {"description": "Assignee field label"}, "status": "Status", "@status": {"description": "Status field label"}, "completedTasks": "Completed Tasks", "@completedTasks": {"description": "Completed tasks metric"}, "clockIn": "Clock in", "@clockIn": {"description": "Clock in button"}, "clockOut": "Clock Out", "@clockOut": {"description": "Clock out button"}, "clockInTime": "Clock in Time", "@clockInTime": {"description": "Clock in time label"}, "totalShiftTime": "Total Shift Time", "@totalShiftTime": {"description": "Total shift time label"}, "overTime": "Overtime Hours:", "@overTime": {"description": "Overtime hours label"}, "breakTime": "Break Time:", "@breakTime": {"description": "Break time label"}, "delay": "Delay:", "@delay": {"description": "Delay label"}, "timeSheet": "TimeSheet", "@timeSheet": {"description": "Timesheet title"}, "customDateRange": "Custom Date Range", "@customDateRange": {"description": "Custom date range filter option"}, "location": "Location", "@location": {"description": "Location label"}, "schedule": "Schedule", "@schedule": {"description": "Schedule section"}, "conferenceCenter": "Conference Center", "@conferenceCenter": {"description": "Conference center label"}, "conferenceDate": "March 15, 2024", "@conferenceDate": {"description": "Conference date"}, "conferenceTime": "10:00 AM - 2:00 PM", "@conferenceTime": {"description": "Conference time"}, "conferenceLocation": "Main Conference Hall", "@conferenceLocation": {"description": "Conference location"}, "salary": "Salary", "@salary": {"description": "Salary title"}, "thisMonthsSalary": "This Month's Salary", "@thisMonthsSalary": {"description": "Current month salary title"}, "netSalary": "Net Salary", "@netSalary": {"description": "Net salary label"}, "expenses": "Expenses", "@expenses": {"description": "Expenses section"}, "expenseTitle": "Monthly Expenses", "@expenseTitle": {"description": "Expense card title"}, "expensesAmount": "500,000", "@expensesAmount": {"description": "Sample expense amount"}, "expensesDate": "December 2024", "@expensesDate": {"description": "Sample expense date"}, "amountIQD": "Amount IQD", "@amountIQD": {"description": "Amount in IQD currency"}, "attachment": "Attachment", "@attachment": {"description": "Attachment section title"}, "fileSelected": "File Selected", "@fileSelected": {"description": "File selected message"}, "otherDocuments": "Other Documents", "@otherDocuments": {"description": "Other documents label"}, "clickToUpload": "Click to Upload", "@clickToUpload": {"description": "Upload prompt text"}, "maxFileSize": "Max file size: 10MB", "@maxFileSize": {"description": "File size limit"}, "selectDocument": "Select Document", "@selectDocument": {"description": "Select document title"}, "selectDocuments": "Select Documents", "@selectDocuments": {"description": "Select documents title"}, "resignationRequest": "Resignation Request", "@resignationRequest": {"description": "Resignation request title"}, "waitingManagerApproval": "Waiting for Manager <PERSON><PERSON><PERSON><PERSON>", "@waitingManagerApproval": {"description": "Pending approval status"}, "feedback": "<PERSON><PERSON><PERSON>", "@feedback": {"description": "Feedback section"}, "managersFeedback": "Manager's <PERSON><PERSON><PERSON>", "@managersFeedback": {"description": "Manager feedback label"}, "feedbackTime": "Feedback Time", "@feedbackTime": {"description": "Feedback time label"}, "feedbackMessage": "Feedback Message", "@feedbackMessage": {"description": "Feedback message label"}, "performance": "Performance", "@performance": {"description": "Performance section"}, "timeOffTaken": "Time Off Taken", "@timeOffTaken": {"description": "Time off taken metric"}, "holidays": "Holidays", "@holidays": {"description": "Holidays metric"}, "other": "Other", "@other": {"description": "Other section"}, "typing": "Typing...", "@typing": {"description": "Typing indicator message"}, "online": "Online", "@online": {"description": "Online status"}, "lastSeenOn": "Last seen on", "@lastSeenOn": {"description": "Last seen prefix"}, "addEmployee": "Add Employee", "@addEmployee": {"description": "Add employee title"}, "members": "Members", "@members": {"description": "Group members label"}, "memberCount": "{count} Members", "@memberCount": {"description": "Member count in group", "placeholders": {"count": {"type": "int", "description": "Number of members"}}}, "pleaseEnterGroupName": "Please enter a group name", "@pleaseEnterGroupName": {"description": "Group name validation message"}, "failedToSelectImage": "Failed to select image. Please try again.", "@failedToSelectImage": {"description": "Image selection error message"}, "newChannelFunctionality": "New Channel functionality will be implemented", "@newChannelFunctionality": {"description": "Placeholder message for new channel"}, "all": "All", "@all": {"description": "All filter option"}, "unread": "Unread", "@unread": {"description": "Unread filter option"}, "teams": "Teams", "@teams": {"description": "Teams filter option"}, "delivered": "Delivered", "@delivered": {"description": "Message delivery status"}, "read": "Read", "@read": {"description": "Message read status"}, "selectContacts": "Select Contacts", "@selectContacts": {"description": "Select contacts title"}, "sendYourLiveLocation": "Send Your Live Location", "@sendYourLiveLocation": {"description": "Live location sharing option"}, "nearbyPlaces": "Nearby Places", "@nearbyPlaces": {"description": "Nearby places section title"}, "sendYourCurrentLocation": "Send Your Current Location", "@sendYourCurrentLocation": {"description": "Current location sharing option"}, "approximateDistance": "Approximate to 14 metres", "@approximateDistance": {"description": "Location accuracy description"}, "yesterday": "Yesterday", "@yesterday": {"description": "Yesterday label for message groups"}, "days": "Days", "@days": {"description": "Days unit label"}, "hours": "Hours", "@hours": {"description": "Hours unit label"}, "thisMonth": "this month", "@thisMonth": {"description": "This month text"}, "jobTitle": "Job Title", "@jobTitle": {"description": "Job title field label"}, "country": "Country", "@country": {"description": "Country field label"}, "dateOfBirth": "Date of Birth", "@dateOfBirth": {"description": "Date of birth field label"}, "iraq": "Iraq", "@iraq": {"description": "Iraq country name"}, "india": "India", "@india": {"description": "India country name"}, "adminContactMessage": "If you wish to edit, please contact the administration", "@adminContactMessage": {"description": "Admin contact message for non-editable fields"}, "logoutConfirmation": "Are You Sure You Want to Log Out?", "@logoutConfirmation": {"description": "Logout confirmation message"}, "chatRequestConfirmation": "Are You Sure You Want to Send the Request?", "@chatRequestConfirmation": {"description": "HR chat request confirmation message"}, "reasonForResignation": "Reason for Resignation", "@reasonForResignation": {"description": "Resignation reason field label"}, "additionalNotes": "Additional Notes", "@additionalNotes": {"description": "Additional notes field label"}, "lastWorkingDay": "Last Working Day", "@lastWorkingDay": {"description": "Last working day field label"}, "submitResignation": "Submit Resignation", "@submitResignation": {"description": "Submit resignation button"}, "overtimeHours": "{hours} Hours this month", "@overtimeHours": {"description": "Overtime hours this month", "placeholders": {"hours": {"type": "int", "description": "Number of overtime hours"}}}, "performanceAndAchievements": "Performance & Achievements", "@performanceAndAchievements": {"description": "Performance and achievements section title"}, "clickToUploadFile": "Click to Upload", "@clickToUploadFile": {"description": "Click to upload file text"}, "maxFileSizeLimit": "(Max file size 25MB)", "@maxFileSizeLimit": {"description": "Maximum file size limit"}, "figmaSubscription": "Figma Subscription", "@figmaSubscription": {"description": "Sample expense title"}, "startWork": "Start Work", "@startWork": {"description": "Start work button label"}, "endWork": "End Work", "@endWork": {"description": "End work button label"}, "shiftDetails": "Shift Details", "@shiftDetails": {"description": "Shift details screen title"}, "drawSignature": "Draw Signature", "@drawSignature": {"description": "Draw signature button"}, "addNote": "Add a Note", "@addNote": {"description": "Add note button"}, "totalHours": "Total Hours", "@totalHours": {"description": "Total hours label"}, "successfullyCompletedShift": "Successfully completed your shift", "@successfullyCompletedShift": {"description": "Shift completion success message"}, "clockOutTime": "Clock Out Time", "@clockOutTime": {"description": "Clock out time label"}, "ifYouClockOutNow": "If You Clock Out Now, You'll be Clock Out at", "@ifYouClockOutNow": {"description": "Clock out warning message"}, "confirmHours": "Confirm Hours", "@confirmHours": {"description": "Confirm hours button"}, "myTasks": "My Tasks", "@myTasks": {"description": "My tasks section title"}, "showAll": "Show All", "@showAll": {"description": "Show all button"}, "requestLeave": "Request for Leave", "@requestLeave": {"description": "Request leave button"}, "timesheet": "Timesheet", "@timesheet": {"description": "Timesheet section"}, "requestExpenses": "Request For Expenses", "@requestExpenses": {"description": "Request expenses button"}, "submit": "Submit", "@submit": {"description": "Submit button"}, "allDay": "All Day", "@allDay": {"description": "All day option"}, "thisMonthFilter": "This Month", "@thisMonthFilter": {"description": "This month filter option"}, "thisWeek": "This Week", "@thisWeek": {"description": "This week filter option"}, "sinceStartOfYear": "Since Start of Year", "@sinceStartOfYear": {"description": "Since start of year filter option"}, "from": "From", "@from": {"description": "From date label"}, "to": "To", "@to": {"description": "To date label"}, "payForThisPeriod": "Pay for this period", "@payForThisPeriod": {"description": "Pay for period label"}, "clockInLabel": "Clock In", "@clockInLabel": {"description": "Clock in label for timesheet"}, "clockOutLabel": "Clock Out", "@clockOutLabel": {"description": "Clock out label for timesheet"}, "breakTimeLabel": "Break Time", "@breakTimeLabel": {"description": "Break time label"}, "sendShiftChangeRequest": "Send Shift Change Request", "@sendShiftChangeRequest": {"description": "Send shift change request button"}, "modelAnswer": "Model Answer", "@modelAnswer": {"description": "Model answer task type"}, "design": "Design", "@design": {"description": "Design task type"}, "backlog": "Backlog", "@backlog": {"description": "Backlog task status"}, "news": "News", "@news": {"description": "News section title"}, "employeeOfTheMonth": "Employee of the Month", "@employeeOfTheMonth": {"description": "Employee of the month title"}, "publishedBy": "Published By HR Manager", "@publishedBy": {"description": "Published by text"}, "today0800Am": "Today 08:00 AM", "@today0800Am": {"description": "Sample time text"}, "howardTheodore": "HOWARD THEODORE", "@howardTheodore": {"description": "Sample employee name"}, "descriptionText": "We are pleased to introduce our new and improved Health Insurance Package, designed with your wellbeing in mind.\n\nAs part of our ongoing commitment to support the health and security of our employees, we have partnered with a new provider to offer a more comprehensive medical plan. This updated package includes:\n\n• Enhanced hospital coverage, including specialist care and private rooms\n• Increased outpatient benefits and faster claim processing\n• Coverage for mental health and wellness services\n• Improved support for family members (spouse and children)\n• A simplified registration and claim procedure via the HR portal", "@descriptionText": {"description": "Sample news description"}, "paySlip": "Pay Slip.pdf", "@paySlip": {"description": "Pay slip file name"}, "maxFileSizePdf": "200 KB", "@maxFileSizePdf": {"description": "Max PDF file size"}, "enterYourNoteHere": "Enter Your Note Here", "@enterYourNoteHere": {"description": "Note input placeholder"}, "companyNews": "Company News", "@companyNews": {"description": "Company news section title"}}