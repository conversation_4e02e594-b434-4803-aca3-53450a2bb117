import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appName => 'أكو بصمة';

  @override
  String get next => 'التالي';

  @override
  String get close => 'إغلاق';

  @override
  String get back => 'رجوع';

  @override
  String get loading => 'جارٍ التحميل...';

  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get edit => 'تحرير';

  @override
  String get delete => 'حذف';

  @override
  String get confirm => 'تأكيد';

  @override
  String get success => 'نجح';

  @override
  String get error => 'خطأ';

  @override
  String get noData => 'لا توجد بيانات متاحة';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get send => 'إرسال';

  @override
  String get create => 'إنشاء';

  @override
  String get reject => 'رفض';

  @override
  String get approve => 'موافق';

  @override
  String get confirmed => 'مؤكد';

  @override
  String get pending => 'في الانتظار';

  @override
  String get paid => 'مدفوع';

  @override
  String get search => 'البحث';

  @override
  String get searching => 'البحث...';

  @override
  String get filter => 'تصفية';

  @override
  String get today => 'اليوم';

  @override
  String get welcome => 'أهلاً وسهلاً';

  @override
  String get home => 'الرئيسية';

  @override
  String get chat => 'المحادثة';

  @override
  String get tasks => 'المهام';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get timeClock => 'ساعة العمل';

  @override
  String get workspace => 'مساحة العمل';

  @override
  String get checkYourMessages => 'تحقق من رسائلك';

  @override
  String get verificationCode => 'لقد أرسلنا رمز التحقق إلى رقم هاتفك';

  @override
  String get resendCode => 'إعادة إرسال الرمز';

  @override
  String get name => 'الاسم';

  @override
  String get userName => 'نادة جعفر عودة';

  @override
  String get userLocation => '10 شارع بلاكستون، لندن، المملكة المتحدة';

  @override
  String get currentClockInTime => '10:30:00';

  @override
  String get role => 'الدور';

  @override
  String get userRole => 'مصمم واجهة المستخدم';

  @override
  String get designation => 'فريق التصميم';

  @override
  String get dob => 'تاريخ الميلاد';

  @override
  String get startDate => 'تاريخ البدء';

  @override
  String get editAccountInfo => 'تحرير معلومات الحساب';

  @override
  String get phoneNumber => 'رقم الهاتف';

  @override
  String get emailAddress => 'عنوان البريد الإلكتروني';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get phone => 'الهاتف';

  @override
  String get accountInfo => 'معلومات الحساب';

  @override
  String get otherInfo => 'معلومات أخرى';

  @override
  String get notEditable => 'غير قابل للتحرير';

  @override
  String get department => 'القسم';

  @override
  String get settings => 'الإعدادات';

  @override
  String get language => 'اللغة';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get notification => 'الإشعار';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get themeMode => 'وضع المظهر';

  @override
  String get reportAProblem => 'الإبلاغ عن مشكلة';

  @override
  String get chatWithHR => 'محادثة مع الموارد البشرية';

  @override
  String get aboutUs => 'عنا';

  @override
  String get termsOfUse => 'شروط الاستخدام';

  @override
  String get about => 'حول';

  @override
  String get newGroup => 'مجموعة جديدة';

  @override
  String get newChannel => 'قناة جديدة';

  @override
  String get ai => 'الذكاء الاصطناعي';

  @override
  String get requestToChatWithHR => 'طلب محادثة مع الموارد البشرية';

  @override
  String get hrGreeting => 'مرحباً! كيف يمكنني مساعدتك اليوم؟';

  @override
  String get hrSampleMessage => 'أردت مناقشة جدول عملي.';

  @override
  String get writeAMessage => 'اكتب رسالة...';

  @override
  String get hrName => 'مدير الموارد البشرية';

  @override
  String get hrLastSeen => 'آخر ظهور منذ ساعتين';

  @override
  String get hrApprovalMessage => 'تم الموافقة على طلبك.';

  @override
  String get youCreatedGroup => 'لقد أنشأت هذه المجموعة';

  @override
  String get call => 'اتصال';

  @override
  String get groupName => 'اسم المجموعة';

  @override
  String get enterGroupName => 'أدخل اسم المجموعة';

  @override
  String get groupDescription => 'وصف المجموعة';

  @override
  String get enterGroupDescription => 'أدخل وصف المجموعة';

  @override
  String get camera => 'الكاميرا';

  @override
  String get record => 'تسجيل';

  @override
  String get contact => 'جهة اتصال';

  @override
  String get gallery => 'المعرض';

  @override
  String get myLocation => 'موقعي';

  @override
  String get document => 'مستند';

  @override
  String get noContactsFound => 'لم يتم العثور على جهات اتصال';

  @override
  String get createTask => 'إنشاء مهمة';

  @override
  String get createProject => 'إنشاء مشروع';

  @override
  String get allProjects => 'جميع المشاريع';

  @override
  String get manageProjects => 'إدارة المشاريع';

  @override
  String get noTasksFound => 'لم يتم العثور على مهام';

  @override
  String get pleaseCreateATask => 'يرجى إنشاء مهمة';

  @override
  String get noProjectFound => 'لم يتم العثور على مشروع';

  @override
  String get pleaseCreateAProject => 'يرجى إنشاء مشروع';

  @override
  String get comments => 'التعليقات';

  @override
  String get canIGetMoreInfo => 'هل يمكنني الحصول على مزيد من المعلومات؟';

  @override
  String get statusUpdate => 'تحديث الحالة';

  @override
  String get taskCompleted => 'تمت المهمة';

  @override
  String get needHelp => 'أحتاج مساعدة';

  @override
  String get approveTask => 'الموافقة على المهمة';

  @override
  String get addComment => 'إضافة تعليق';

  @override
  String get taskDetails => 'تفاصيل المهمة';

  @override
  String get title => 'العنوان';

  @override
  String get description => 'الوصف';

  @override
  String get assignee => 'المكلف';

  @override
  String get status => 'الحالة';

  @override
  String get completedTasks => 'المهام المكتملة';

  @override
  String get clockIn => 'تسجيل الحضور';

  @override
  String get clockOut => 'تسجيل الخروج';

  @override
  String get clockInTime => 'وقت تسجيل الحضور';

  @override
  String get totalShiftTime => 'إجمالي وقت الوردية';

  @override
  String get overTime => 'الوقت الإضافي:';

  @override
  String get breakTime => 'وقت الاستراحة:';

  @override
  String get delay => 'التأخير:';

  @override
  String get timeSheet => 'كشف الوقت';

  @override
  String get customDateRange => 'نطاق تاريخ مخصص';

  @override
  String get location => 'الموقع';

  @override
  String get schedule => 'الجدولة';

  @override
  String get conferenceCenter => 'مركز المؤتمرات';

  @override
  String get conferenceDate => '15 مارس 2024';

  @override
  String get conferenceTime => '10:00 صباحاً - 2:00 بعد الظهر';

  @override
  String get conferenceLocation => 'قاعة المؤتمرات الرئيسية';

  @override
  String get salary => 'الراتب';

  @override
  String get thisMonthsSalary => 'راتب هذا الشهر';

  @override
  String get netSalary => 'الراتب الصافي';

  @override
  String get expenses => 'المصروفات';

  @override
  String get expenseTitle => 'عنوان المصروف';

  @override
  String get expensesAmount => '350,000';

  @override
  String get expensesDate => '25 نوفمبر 2024';

  @override
  String get amountIQD => 'المبلغ (دينار عراقي)';

  @override
  String get attachment => 'المرفق';

  @override
  String get fileSelected => 'تم اختيار الملف';

  @override
  String get otherDocuments => 'مستندات أخرى';

  @override
  String get clickToUpload => 'انقر للرفع';

  @override
  String get maxFileSize => 'حد أقصى للملف 10MB';

  @override
  String get selectDocument => 'اختيار مستند';

  @override
  String get selectDocuments => 'اختيار مستندات';

  @override
  String get resignationRequest => 'طلب الاستقالة';

  @override
  String get waitingManagerApproval => 'في انتظار موافقة المدير';

  @override
  String get feedback => 'التعليقات';

  @override
  String get managersFeedback => 'تعليقات المدير';

  @override
  String get feedbackTime => 'وقت التعليق';

  @override
  String get feedbackMessage => 'رسالة التعليق';

  @override
  String get performance => 'الأداء';

  @override
  String get timeOffTaken => 'الإجازات المأخوذة';

  @override
  String get holidays => 'العطل';

  @override
  String get other => 'أخرى';

  @override
  String get typing => 'يكتب...';

  @override
  String get online => 'متصل';

  @override
  String get lastSeenOn => 'آخر ظهور في';

  @override
  String get addEmployee => 'إضافة موظف';

  @override
  String get members => 'الأعضاء';

  @override
  String memberCount(int count) {
    return '$count أعضاء';
  }

  @override
  String get pleaseEnterGroupName => 'يرجى إدخال اسم المجموعة';

  @override
  String get failedToSelectImage => 'فشل في اختيار الصورة. يرجى المحاولة مرة أخرى.';

  @override
  String get newChannelFunctionality => 'سيتم تنفيذ وظيفة القناة الجديدة';

  @override
  String get all => 'الكل';

  @override
  String get unread => 'غير مقروء';

  @override
  String get teams => 'الفرق';

  @override
  String get delivered => 'تم التسليم';

  @override
  String get read => 'مقروء';

  @override
  String get selectContacts => 'اختيار جهات الاتصال';

  @override
  String get sendYourLiveLocation => 'إرسال موقعك المباشر';

  @override
  String get nearbyPlaces => 'الأماكن القريبة';

  @override
  String get sendYourCurrentLocation => 'إرسال موقعك الحالي';

  @override
  String get approximateDistance => 'تقريباً 14 متر';

  @override
  String get yesterday => 'أمس';

  @override
  String get days => 'أيام';

  @override
  String get hours => 'ساعات';

  @override
  String get thisMonth => 'هذا الشهر';

  @override
  String get jobTitle => 'المسمى الوظيفي';

  @override
  String get country => 'البلد';

  @override
  String get dateOfBirth => 'تاريخ الميلاد';

  @override
  String get iraq => 'العراق';

  @override
  String get india => 'الهند';

  @override
  String get adminContactMessage => 'إذا كنت ترغب في التعديل، يرجى التواصل مع الإدارة';

  @override
  String get logoutConfirmation => 'هل أنت متأكد أنك تريد تسجيل الخروج؟';

  @override
  String get chatRequestConfirmation => 'هل أنت متأكد أنك تريد إرسال الطلب؟';

  @override
  String get reasonForResignation => 'سبب الاستقالة';

  @override
  String get additionalNotes => 'ملاحظات إضافية';

  @override
  String get lastWorkingDay => 'آخر يوم عمل';

  @override
  String get submitResignation => 'تقديم الاستقالة';

  @override
  String overtimeHours(int hours) {
    return '$hours ساعات هذا الشهر';
  }

  @override
  String get performanceAndAchievements => 'الأداء والإنجازات';

  @override
  String get clickToUploadFile => 'انقر للرفع';

  @override
  String get maxFileSizeLimit => '(حد أقصى للملف 25 ميجابايت)';

  @override
  String get figmaSubscription => 'اشتراك Figma';

  @override
  String get startWork => 'بدء العمل';

  @override
  String get endWork => 'انتهاء العمل';

  @override
  String get shiftDetails => 'تفاصيل الوردية';

  @override
  String get drawSignature => 'رسم التوقيع';

  @override
  String get addNote => 'إضافة ملاحظة';

  @override
  String get totalHours => 'إجمالي الساعات';

  @override
  String get successfullyCompletedShift => 'تم إنهاء وردية العمل بنجاح';

  @override
  String get clockOutTime => 'وقت تسجيل الخروج';

  @override
  String get ifYouClockOutNow => 'إذا قمت بتسجيل الخروج الآن، ستكون قد خرجت في';

  @override
  String get confirmHours => 'تأكيد الساعات';

  @override
  String get myTasks => 'مهامي';

  @override
  String get showAll => 'عرض الكل';

  @override
  String get requestLeave => 'طلب إجازة';

  @override
  String get timesheet => 'كشف الوقت';

  @override
  String get requestExpenses => 'طلب المصروفات';

  @override
  String get submit => 'إرسال';

  @override
  String get allDay => 'طوال اليوم';

  @override
  String get thisMonthFilter => 'هذا الشهر';

  @override
  String get thisWeek => 'هذا الأسبوع';

  @override
  String get sinceStartOfYear => 'منذ بداية السنة';

  @override
  String get from => 'من';

  @override
  String get to => 'إلى';

  @override
  String get payForThisPeriod => 'راتب هذه الفترة';

  @override
  String get clockInLabel => 'تسجيل الدخول';

  @override
  String get clockOutLabel => 'تسجيل الخروج';

  @override
  String get breakTimeLabel => 'وقت الاستراحة';

  @override
  String get sendShiftChangeRequest => 'إرسال طلب تغيير الوردية';

  @override
  String get modelAnswer => 'إجابة نموذجية';

  @override
  String get design => 'تصميم';

  @override
  String get backlog => 'متأخر';

  @override
  String get news => 'الأخبار';

  @override
  String get employeeOfTheMonth => 'موظف الشهر';

  @override
  String get publishedBy => 'نشر بواسطة مدير الموارد البشرية';

  @override
  String get today0800Am => 'اليوم 08:00 صباحاً';

  @override
  String get howardTheodore => 'هوارد ثيودور';

  @override
  String get descriptionText => 'يسعدنا أن نقدم لكم باقة التأمين الصحي الجديدة والمحسنة، المصممة خصيصاً لرفاهيتكم.\n\nكجزء من التزامنا المستمر لدعم صحة وأمان موظفينا، تشاركنا مع مقدم خدمة جديد لتقديم خطة طبية أكثر شمولية. تشمل هذه الباقة المحدثة:\n\n• تغطية محسنة للمستشفيات، بما في ذلك الرعاية المتخصصة والغرف الخاصة\n• فوائد محسنة للمرضى الخارجيين ومعالجة أسرع للمطالبات\n• تغطية للصحة النفسية وخدمات العافية\n• دعم محسن لأفراد الأسرة (الزوج والأطفال)\n• إجراء تسجيل ومطالبة مبسط عبر بوابة الموارد البشرية';

  @override
  String get paySlip => 'قسيمة الراتب.pdf';

  @override
  String get maxFileSizePdf => '200 كيلوبايت';

  @override
  String get enterYourNoteHere => 'أدخل ملاحظتك هنا';

  @override
  String get companyNews => 'أخبار الشركة';
}
