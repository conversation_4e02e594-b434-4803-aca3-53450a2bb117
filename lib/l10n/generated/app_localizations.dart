import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en')
  ];

  /// The name of the application
  ///
  /// In en, this message translates to:
  /// **'Ako Basma'**
  String get appName;

  /// Next button text
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// Close button text
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// Back button text
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;

  /// Loading indicator text
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// Save button
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// Cancel button text
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// Edit button text
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// Delete button text
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// Confirm button text
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// Success message
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// Error message
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// No data message
  ///
  /// In en, this message translates to:
  /// **'No data available'**
  String get noData;

  /// Retry button text
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// Send button
  ///
  /// In en, this message translates to:
  /// **'Send'**
  String get send;

  /// Create button text
  ///
  /// In en, this message translates to:
  /// **'Create'**
  String get create;

  /// Reject button
  ///
  /// In en, this message translates to:
  /// **'Reject'**
  String get reject;

  /// Approve button
  ///
  /// In en, this message translates to:
  /// **'Approve'**
  String get approve;

  /// Confirmed status
  ///
  /// In en, this message translates to:
  /// **'Confirmed'**
  String get confirmed;

  /// Pending status
  ///
  /// In en, this message translates to:
  /// **'Pending'**
  String get pending;

  /// Payment status
  ///
  /// In en, this message translates to:
  /// **'Paid'**
  String get paid;

  /// Search placeholder text
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// Search placeholder text
  ///
  /// In en, this message translates to:
  /// **'Searching...'**
  String get searching;

  /// Filter option
  ///
  /// In en, this message translates to:
  /// **'Filter'**
  String get filter;

  /// Today filter option
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get today;

  /// Welcome greeting text
  ///
  /// In en, this message translates to:
  /// **'Welcome'**
  String get welcome;

  /// Home tab label
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// Chat tab label
  ///
  /// In en, this message translates to:
  /// **'Chat'**
  String get chat;

  /// Tasks tab label
  ///
  /// In en, this message translates to:
  /// **'Tasks'**
  String get tasks;

  /// Profile tab label
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// Time Clock tab
  ///
  /// In en, this message translates to:
  /// **'Time Clock'**
  String get timeClock;

  /// Workspace tab
  ///
  /// In en, this message translates to:
  /// **'Workspace'**
  String get workspace;

  /// OTP screen title
  ///
  /// In en, this message translates to:
  /// **'Check Your Messages'**
  String get checkYourMessages;

  /// Verification code instruction
  ///
  /// In en, this message translates to:
  /// **'We\'ve sent a verification code to your phone number'**
  String get verificationCode;

  /// Resend verification code button
  ///
  /// In en, this message translates to:
  /// **'Resend Code'**
  String get resendCode;

  /// Name field
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// Actual user name
  ///
  /// In en, this message translates to:
  /// **'Nada Jaafar Uday'**
  String get userName;

  /// User's current work location
  ///
  /// In en, this message translates to:
  /// **'10 Blackstone Street, London, UK'**
  String get userLocation;

  /// Current clock in time
  ///
  /// In en, this message translates to:
  /// **'10:30:00'**
  String get currentClockInTime;

  /// Role field
  ///
  /// In en, this message translates to:
  /// **'Role'**
  String get role;

  /// Actual user role
  ///
  /// In en, this message translates to:
  /// **'UI/UX Designer'**
  String get userRole;

  /// Designation field
  ///
  /// In en, this message translates to:
  /// **'Design Team'**
  String get designation;

  /// Date of birth
  ///
  /// In en, this message translates to:
  /// **'Date of Birth'**
  String get dob;

  /// Start date
  ///
  /// In en, this message translates to:
  /// **'Start Date'**
  String get startDate;

  /// Edit account info button
  ///
  /// In en, this message translates to:
  /// **'Edit Account Info'**
  String get editAccountInfo;

  /// Phone number field
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phoneNumber;

  /// Email address field
  ///
  /// In en, this message translates to:
  /// **'Email Address'**
  String get emailAddress;

  /// Email label
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// Phone label
  ///
  /// In en, this message translates to:
  /// **'Phone'**
  String get phone;

  /// Account info section
  ///
  /// In en, this message translates to:
  /// **'Account Info'**
  String get accountInfo;

  /// Other info section
  ///
  /// In en, this message translates to:
  /// **'Other Info'**
  String get otherInfo;

  /// Not editable message
  ///
  /// In en, this message translates to:
  /// **'Not Editable'**
  String get notEditable;

  /// Department field label
  ///
  /// In en, this message translates to:
  /// **'Department'**
  String get department;

  /// Settings title
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// Language setting
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// Notifications setting
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// Notification title
  ///
  /// In en, this message translates to:
  /// **'Notification'**
  String get notification;

  /// Logout button text
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// Theme mode setting
  ///
  /// In en, this message translates to:
  /// **'Theme Mode'**
  String get themeMode;

  /// Report problem option
  ///
  /// In en, this message translates to:
  /// **'Report A Problem'**
  String get reportAProblem;

  /// Chat with HR option
  ///
  /// In en, this message translates to:
  /// **'Chat With HR'**
  String get chatWithHR;

  /// About us option
  ///
  /// In en, this message translates to:
  /// **'About Us'**
  String get aboutUs;

  /// Terms of use option
  ///
  /// In en, this message translates to:
  /// **'Terms of Use'**
  String get termsOfUse;

  /// About section
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get about;

  /// New group option
  ///
  /// In en, this message translates to:
  /// **'New Group'**
  String get newGroup;

  /// New channel option
  ///
  /// In en, this message translates to:
  /// **'New Channel'**
  String get newChannel;

  /// AI label
  ///
  /// In en, this message translates to:
  /// **'AI'**
  String get ai;

  /// HR chat request title
  ///
  /// In en, this message translates to:
  /// **'Request to Chat with HR'**
  String get requestToChatWithHR;

  /// HR greeting message
  ///
  /// In en, this message translates to:
  /// **'Hello! How can I help you today?'**
  String get hrGreeting;

  /// HR sample message
  ///
  /// In en, this message translates to:
  /// **'I wanted to discuss my work schedule.'**
  String get hrSampleMessage;

  /// Message input placeholder
  ///
  /// In en, this message translates to:
  /// **'Write a message...'**
  String get writeAMessage;

  /// HR manager name
  ///
  /// In en, this message translates to:
  /// **'HR Manager'**
  String get hrName;

  /// HR last seen status
  ///
  /// In en, this message translates to:
  /// **'Last seen 2 hours ago'**
  String get hrLastSeen;

  /// HR approval message
  ///
  /// In en, this message translates to:
  /// **'Your request has been approved.'**
  String get hrApprovalMessage;

  /// Group creation message
  ///
  /// In en, this message translates to:
  /// **'You created this group'**
  String get youCreatedGroup;

  /// Call action
  ///
  /// In en, this message translates to:
  /// **'Call'**
  String get call;

  /// Group name field
  ///
  /// In en, this message translates to:
  /// **'Group Name'**
  String get groupName;

  /// Group name placeholder
  ///
  /// In en, this message translates to:
  /// **'Enter group name'**
  String get enterGroupName;

  /// Group description field
  ///
  /// In en, this message translates to:
  /// **'Group Description'**
  String get groupDescription;

  /// Group description placeholder
  ///
  /// In en, this message translates to:
  /// **'Enter group description'**
  String get enterGroupDescription;

  /// Camera option
  ///
  /// In en, this message translates to:
  /// **'Camera'**
  String get camera;

  /// Record option
  ///
  /// In en, this message translates to:
  /// **'Record'**
  String get record;

  /// Contact option
  ///
  /// In en, this message translates to:
  /// **'Contact'**
  String get contact;

  /// Gallery option
  ///
  /// In en, this message translates to:
  /// **'Gallery'**
  String get gallery;

  /// Location option
  ///
  /// In en, this message translates to:
  /// **'My Location'**
  String get myLocation;

  /// Document option
  ///
  /// In en, this message translates to:
  /// **'Document'**
  String get document;

  /// Empty state message
  ///
  /// In en, this message translates to:
  /// **'No contacts found'**
  String get noContactsFound;

  /// Create task button
  ///
  /// In en, this message translates to:
  /// **'Create Task'**
  String get createTask;

  /// Create project button
  ///
  /// In en, this message translates to:
  /// **'Create Project'**
  String get createProject;

  /// All projects filter
  ///
  /// In en, this message translates to:
  /// **'All Projects'**
  String get allProjects;

  /// Manage projects option
  ///
  /// In en, this message translates to:
  /// **'Manage Projects'**
  String get manageProjects;

  /// No tasks message
  ///
  /// In en, this message translates to:
  /// **'No Tasks Found'**
  String get noTasksFound;

  /// Create task prompt
  ///
  /// In en, this message translates to:
  /// **'Please create a task'**
  String get pleaseCreateATask;

  /// No project message
  ///
  /// In en, this message translates to:
  /// **'No Project Found'**
  String get noProjectFound;

  /// Create project prompt
  ///
  /// In en, this message translates to:
  /// **'Please create a project'**
  String get pleaseCreateAProject;

  /// Comments section title
  ///
  /// In en, this message translates to:
  /// **'Comments'**
  String get comments;

  /// Request info comment
  ///
  /// In en, this message translates to:
  /// **'Can I get more info?'**
  String get canIGetMoreInfo;

  /// Status update comment
  ///
  /// In en, this message translates to:
  /// **'Status Update'**
  String get statusUpdate;

  /// Task completed comment
  ///
  /// In en, this message translates to:
  /// **'Task Completed'**
  String get taskCompleted;

  /// Need help comment
  ///
  /// In en, this message translates to:
  /// **'Need Help'**
  String get needHelp;

  /// Approve task comment
  ///
  /// In en, this message translates to:
  /// **'Approve Task'**
  String get approveTask;

  /// Add comment button
  ///
  /// In en, this message translates to:
  /// **'Add Comment'**
  String get addComment;

  /// Task details header
  ///
  /// In en, this message translates to:
  /// **'Task Details'**
  String get taskDetails;

  /// Title field label
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get title;

  /// Description label
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// Assignee field label
  ///
  /// In en, this message translates to:
  /// **'Assignee'**
  String get assignee;

  /// Status field label
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// Completed tasks metric
  ///
  /// In en, this message translates to:
  /// **'Completed Tasks'**
  String get completedTasks;

  /// Clock in button
  ///
  /// In en, this message translates to:
  /// **'Clock in'**
  String get clockIn;

  /// Clock out button
  ///
  /// In en, this message translates to:
  /// **'Clock Out'**
  String get clockOut;

  /// Clock in time label
  ///
  /// In en, this message translates to:
  /// **'Clock in Time'**
  String get clockInTime;

  /// Total shift time label
  ///
  /// In en, this message translates to:
  /// **'Total Shift Time'**
  String get totalShiftTime;

  /// Overtime hours label
  ///
  /// In en, this message translates to:
  /// **'Overtime Hours:'**
  String get overTime;

  /// Break time label
  ///
  /// In en, this message translates to:
  /// **'Break Time:'**
  String get breakTime;

  /// Delay label
  ///
  /// In en, this message translates to:
  /// **'Delay:'**
  String get delay;

  /// Timesheet title
  ///
  /// In en, this message translates to:
  /// **'TimeSheet'**
  String get timeSheet;

  /// Custom date range filter option
  ///
  /// In en, this message translates to:
  /// **'Custom Date Range'**
  String get customDateRange;

  /// Location label
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get location;

  /// Schedule section
  ///
  /// In en, this message translates to:
  /// **'Schedule'**
  String get schedule;

  /// Conference center label
  ///
  /// In en, this message translates to:
  /// **'Conference Center'**
  String get conferenceCenter;

  /// Conference date
  ///
  /// In en, this message translates to:
  /// **'March 15, 2024'**
  String get conferenceDate;

  /// Conference time
  ///
  /// In en, this message translates to:
  /// **'10:00 AM - 2:00 PM'**
  String get conferenceTime;

  /// Conference location
  ///
  /// In en, this message translates to:
  /// **'Main Conference Hall'**
  String get conferenceLocation;

  /// Salary title
  ///
  /// In en, this message translates to:
  /// **'Salary'**
  String get salary;

  /// Current month salary title
  ///
  /// In en, this message translates to:
  /// **'This Month\'s Salary'**
  String get thisMonthsSalary;

  /// Net salary label
  ///
  /// In en, this message translates to:
  /// **'Net Salary'**
  String get netSalary;

  /// Expenses section
  ///
  /// In en, this message translates to:
  /// **'Expenses'**
  String get expenses;

  /// Expense card title
  ///
  /// In en, this message translates to:
  /// **'Monthly Expenses'**
  String get expenseTitle;

  /// Sample expense amount
  ///
  /// In en, this message translates to:
  /// **'500,000'**
  String get expensesAmount;

  /// Sample expense date
  ///
  /// In en, this message translates to:
  /// **'December 2024'**
  String get expensesDate;

  /// Amount in IQD currency
  ///
  /// In en, this message translates to:
  /// **'Amount IQD'**
  String get amountIQD;

  /// Attachment section title
  ///
  /// In en, this message translates to:
  /// **'Attachment'**
  String get attachment;

  /// File selected message
  ///
  /// In en, this message translates to:
  /// **'File Selected'**
  String get fileSelected;

  /// Other documents label
  ///
  /// In en, this message translates to:
  /// **'Other Documents'**
  String get otherDocuments;

  /// Upload prompt text
  ///
  /// In en, this message translates to:
  /// **'Click to Upload'**
  String get clickToUpload;

  /// File size limit
  ///
  /// In en, this message translates to:
  /// **'Max file size: 10MB'**
  String get maxFileSize;

  /// Select document title
  ///
  /// In en, this message translates to:
  /// **'Select Document'**
  String get selectDocument;

  /// Select documents title
  ///
  /// In en, this message translates to:
  /// **'Select Documents'**
  String get selectDocuments;

  /// Resignation request title
  ///
  /// In en, this message translates to:
  /// **'Resignation Request'**
  String get resignationRequest;

  /// Pending approval status
  ///
  /// In en, this message translates to:
  /// **'Waiting for Manager Approval'**
  String get waitingManagerApproval;

  /// Feedback section
  ///
  /// In en, this message translates to:
  /// **'Feedback'**
  String get feedback;

  /// Manager feedback label
  ///
  /// In en, this message translates to:
  /// **'Manager\'s Feedback'**
  String get managersFeedback;

  /// Feedback time label
  ///
  /// In en, this message translates to:
  /// **'Feedback Time'**
  String get feedbackTime;

  /// Feedback message label
  ///
  /// In en, this message translates to:
  /// **'Feedback Message'**
  String get feedbackMessage;

  /// Performance section
  ///
  /// In en, this message translates to:
  /// **'Performance'**
  String get performance;

  /// Time off taken metric
  ///
  /// In en, this message translates to:
  /// **'Time Off Taken'**
  String get timeOffTaken;

  /// Holidays metric
  ///
  /// In en, this message translates to:
  /// **'Holidays'**
  String get holidays;

  /// Other section
  ///
  /// In en, this message translates to:
  /// **'Other'**
  String get other;

  /// Typing indicator message
  ///
  /// In en, this message translates to:
  /// **'Typing...'**
  String get typing;

  /// Online status
  ///
  /// In en, this message translates to:
  /// **'Online'**
  String get online;

  /// Last seen prefix
  ///
  /// In en, this message translates to:
  /// **'Last seen on'**
  String get lastSeenOn;

  /// Add employee title
  ///
  /// In en, this message translates to:
  /// **'Add Employee'**
  String get addEmployee;

  /// Group members label
  ///
  /// In en, this message translates to:
  /// **'Members'**
  String get members;

  /// Member count in group
  ///
  /// In en, this message translates to:
  /// **'{count} Members'**
  String memberCount(int count);

  /// Group name validation message
  ///
  /// In en, this message translates to:
  /// **'Please enter a group name'**
  String get pleaseEnterGroupName;

  /// Image selection error message
  ///
  /// In en, this message translates to:
  /// **'Failed to select image. Please try again.'**
  String get failedToSelectImage;

  /// Placeholder message for new channel
  ///
  /// In en, this message translates to:
  /// **'New Channel functionality will be implemented'**
  String get newChannelFunctionality;

  /// All filter option
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// Unread filter option
  ///
  /// In en, this message translates to:
  /// **'Unread'**
  String get unread;

  /// Teams filter option
  ///
  /// In en, this message translates to:
  /// **'Teams'**
  String get teams;

  /// Message delivery status
  ///
  /// In en, this message translates to:
  /// **'Delivered'**
  String get delivered;

  /// Message read status
  ///
  /// In en, this message translates to:
  /// **'Read'**
  String get read;

  /// Select contacts title
  ///
  /// In en, this message translates to:
  /// **'Select Contacts'**
  String get selectContacts;

  /// Live location sharing option
  ///
  /// In en, this message translates to:
  /// **'Send Your Live Location'**
  String get sendYourLiveLocation;

  /// Nearby places section title
  ///
  /// In en, this message translates to:
  /// **'Nearby Places'**
  String get nearbyPlaces;

  /// Current location sharing option
  ///
  /// In en, this message translates to:
  /// **'Send Your Current Location'**
  String get sendYourCurrentLocation;

  /// Location accuracy description
  ///
  /// In en, this message translates to:
  /// **'Approximate to 14 metres'**
  String get approximateDistance;

  /// Yesterday label for message groups
  ///
  /// In en, this message translates to:
  /// **'Yesterday'**
  String get yesterday;

  /// Days unit label
  ///
  /// In en, this message translates to:
  /// **'Days'**
  String get days;

  /// Hours unit label
  ///
  /// In en, this message translates to:
  /// **'Hours'**
  String get hours;

  /// This month text
  ///
  /// In en, this message translates to:
  /// **'this month'**
  String get thisMonth;

  /// Job title field label
  ///
  /// In en, this message translates to:
  /// **'Job Title'**
  String get jobTitle;

  /// Country field label
  ///
  /// In en, this message translates to:
  /// **'Country'**
  String get country;

  /// Date of birth field label
  ///
  /// In en, this message translates to:
  /// **'Date of Birth'**
  String get dateOfBirth;

  /// Iraq country name
  ///
  /// In en, this message translates to:
  /// **'Iraq'**
  String get iraq;

  /// India country name
  ///
  /// In en, this message translates to:
  /// **'India'**
  String get india;

  /// Admin contact message for non-editable fields
  ///
  /// In en, this message translates to:
  /// **'If you wish to edit, please contact the administration'**
  String get adminContactMessage;

  /// Logout confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are You Sure You Want to Log Out?'**
  String get logoutConfirmation;

  /// HR chat request confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are You Sure You Want to Send the Request?'**
  String get chatRequestConfirmation;

  /// Resignation reason field label
  ///
  /// In en, this message translates to:
  /// **'Reason for Resignation'**
  String get reasonForResignation;

  /// Additional notes field label
  ///
  /// In en, this message translates to:
  /// **'Additional Notes'**
  String get additionalNotes;

  /// Last working day field label
  ///
  /// In en, this message translates to:
  /// **'Last Working Day'**
  String get lastWorkingDay;

  /// Submit resignation button
  ///
  /// In en, this message translates to:
  /// **'Submit Resignation'**
  String get submitResignation;

  /// Overtime hours this month
  ///
  /// In en, this message translates to:
  /// **'{hours} Hours this month'**
  String overtimeHours(int hours);

  /// Performance and achievements section title
  ///
  /// In en, this message translates to:
  /// **'Performance & Achievements'**
  String get performanceAndAchievements;

  /// Click to upload file text
  ///
  /// In en, this message translates to:
  /// **'Click to Upload'**
  String get clickToUploadFile;

  /// Maximum file size limit
  ///
  /// In en, this message translates to:
  /// **'(Max file size 25MB)'**
  String get maxFileSizeLimit;

  /// Sample expense title
  ///
  /// In en, this message translates to:
  /// **'Figma Subscription'**
  String get figmaSubscription;

  /// Start work button label
  ///
  /// In en, this message translates to:
  /// **'Start Work'**
  String get startWork;

  /// End work button label
  ///
  /// In en, this message translates to:
  /// **'End Work'**
  String get endWork;

  /// Shift details screen title
  ///
  /// In en, this message translates to:
  /// **'Shift Details'**
  String get shiftDetails;

  /// Draw signature button
  ///
  /// In en, this message translates to:
  /// **'Draw Signature'**
  String get drawSignature;

  /// Add note button
  ///
  /// In en, this message translates to:
  /// **'Add a Note'**
  String get addNote;

  /// Total hours label
  ///
  /// In en, this message translates to:
  /// **'Total Hours'**
  String get totalHours;

  /// Shift completion success message
  ///
  /// In en, this message translates to:
  /// **'Successfully completed your shift'**
  String get successfullyCompletedShift;

  /// Clock out time label
  ///
  /// In en, this message translates to:
  /// **'Clock Out Time'**
  String get clockOutTime;

  /// Clock out warning message
  ///
  /// In en, this message translates to:
  /// **'If You Clock Out Now, You\'ll be Clock Out at'**
  String get ifYouClockOutNow;

  /// Confirm hours button
  ///
  /// In en, this message translates to:
  /// **'Confirm Hours'**
  String get confirmHours;

  /// My tasks section title
  ///
  /// In en, this message translates to:
  /// **'My Tasks'**
  String get myTasks;

  /// Show all button
  ///
  /// In en, this message translates to:
  /// **'Show All'**
  String get showAll;

  /// Request leave button
  ///
  /// In en, this message translates to:
  /// **'Request for Leave'**
  String get requestLeave;

  /// Timesheet section
  ///
  /// In en, this message translates to:
  /// **'Timesheet'**
  String get timesheet;

  /// Request expenses button
  ///
  /// In en, this message translates to:
  /// **'Request For Expenses'**
  String get requestExpenses;

  /// Submit button
  ///
  /// In en, this message translates to:
  /// **'Submit'**
  String get submit;

  /// All day option
  ///
  /// In en, this message translates to:
  /// **'All Day'**
  String get allDay;

  /// This month filter option
  ///
  /// In en, this message translates to:
  /// **'This Month'**
  String get thisMonthFilter;

  /// This week filter option
  ///
  /// In en, this message translates to:
  /// **'This Week'**
  String get thisWeek;

  /// Since start of year filter option
  ///
  /// In en, this message translates to:
  /// **'Since Start of Year'**
  String get sinceStartOfYear;

  /// From date label
  ///
  /// In en, this message translates to:
  /// **'From'**
  String get from;

  /// To date label
  ///
  /// In en, this message translates to:
  /// **'To'**
  String get to;

  /// Pay for period label
  ///
  /// In en, this message translates to:
  /// **'Pay for this period'**
  String get payForThisPeriod;

  /// Clock in label for timesheet
  ///
  /// In en, this message translates to:
  /// **'Clock In'**
  String get clockInLabel;

  /// Clock out label for timesheet
  ///
  /// In en, this message translates to:
  /// **'Clock Out'**
  String get clockOutLabel;

  /// Break time label
  ///
  /// In en, this message translates to:
  /// **'Break Time'**
  String get breakTimeLabel;

  /// Send shift change request button
  ///
  /// In en, this message translates to:
  /// **'Send Shift Change Request'**
  String get sendShiftChangeRequest;

  /// Model answer task type
  ///
  /// In en, this message translates to:
  /// **'Model Answer'**
  String get modelAnswer;

  /// Design task type
  ///
  /// In en, this message translates to:
  /// **'Design'**
  String get design;

  /// Backlog task status
  ///
  /// In en, this message translates to:
  /// **'Backlog'**
  String get backlog;

  /// News section title
  ///
  /// In en, this message translates to:
  /// **'News'**
  String get news;

  /// Employee of the month title
  ///
  /// In en, this message translates to:
  /// **'Employee of the Month'**
  String get employeeOfTheMonth;

  /// Published by text
  ///
  /// In en, this message translates to:
  /// **'Published By HR Manager'**
  String get publishedBy;

  /// Sample time text
  ///
  /// In en, this message translates to:
  /// **'Today 08:00 AM'**
  String get today0800Am;

  /// Sample employee name
  ///
  /// In en, this message translates to:
  /// **'HOWARD THEODORE'**
  String get howardTheodore;

  /// Sample news description
  ///
  /// In en, this message translates to:
  /// **'We are pleased to introduce our new and improved Health Insurance Package, designed with your wellbeing in mind.\n\nAs part of our ongoing commitment to support the health and security of our employees, we have partnered with a new provider to offer a more comprehensive medical plan. This updated package includes:\n\n• Enhanced hospital coverage, including specialist care and private rooms\n• Increased outpatient benefits and faster claim processing\n• Coverage for mental health and wellness services\n• Improved support for family members (spouse and children)\n• A simplified registration and claim procedure via the HR portal'**
  String get descriptionText;

  /// Pay slip file name
  ///
  /// In en, this message translates to:
  /// **'Pay Slip.pdf'**
  String get paySlip;

  /// Max PDF file size
  ///
  /// In en, this message translates to:
  /// **'200 KB'**
  String get maxFileSizePdf;

  /// Note input placeholder
  ///
  /// In en, this message translates to:
  /// **'Enter Your Note Here'**
  String get enterYourNoteHere;

  /// Company news section title
  ///
  /// In en, this message translates to:
  /// **'Company News'**
  String get companyNews;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['ar', 'en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar': return AppLocalizationsAr();
    case 'en': return AppLocalizationsEn();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
