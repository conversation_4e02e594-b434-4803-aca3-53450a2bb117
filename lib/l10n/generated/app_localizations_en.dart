import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'Ako Basma';

  @override
  String get next => 'Next';

  @override
  String get close => 'Close';

  @override
  String get back => 'Back';

  @override
  String get loading => 'Loading...';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get edit => 'Edit';

  @override
  String get delete => 'Delete';

  @override
  String get confirm => 'Confirm';

  @override
  String get success => 'Success';

  @override
  String get error => 'Error';

  @override
  String get noData => 'No data available';

  @override
  String get retry => 'Retry';

  @override
  String get send => 'Send';

  @override
  String get create => 'Create';

  @override
  String get reject => 'Reject';

  @override
  String get approve => 'Approve';

  @override
  String get confirmed => 'Confirmed';

  @override
  String get pending => 'Pending';

  @override
  String get paid => 'Paid';

  @override
  String get search => 'Search';

  @override
  String get searching => 'Searching...';

  @override
  String get filter => 'Filter';

  @override
  String get today => 'Today';

  @override
  String get welcome => 'Welcome';

  @override
  String get home => 'Home';

  @override
  String get chat => 'Chat';

  @override
  String get tasks => 'Tasks';

  @override
  String get profile => 'Profile';

  @override
  String get timeClock => 'Time Clock';

  @override
  String get workspace => 'Workspace';

  @override
  String get checkYourMessages => 'Check Your Messages';

  @override
  String get verificationCode => 'We\'ve sent a verification code to your phone number';

  @override
  String get resendCode => 'Resend Code';

  @override
  String get name => 'Name';

  @override
  String get userName => 'Nada Jaafar Uday';

  @override
  String get userLocation => '10 Blackstone Street, London, UK';

  @override
  String get currentClockInTime => '10:30:00';

  @override
  String get role => 'Role';

  @override
  String get userRole => 'UI/UX Designer';

  @override
  String get designation => 'Design Team';

  @override
  String get dob => 'Date of Birth';

  @override
  String get startDate => 'Start Date';

  @override
  String get editAccountInfo => 'Edit Account Info';

  @override
  String get phoneNumber => 'Phone Number';

  @override
  String get emailAddress => 'Email Address';

  @override
  String get email => 'Email';

  @override
  String get phone => 'Phone';

  @override
  String get accountInfo => 'Account Info';

  @override
  String get otherInfo => 'Other Info';

  @override
  String get notEditable => 'Not Editable';

  @override
  String get department => 'Department';

  @override
  String get settings => 'Settings';

  @override
  String get language => 'Language';

  @override
  String get notifications => 'Notifications';

  @override
  String get notification => 'Notification';

  @override
  String get logout => 'Logout';

  @override
  String get themeMode => 'Theme Mode';

  @override
  String get reportAProblem => 'Report A Problem';

  @override
  String get chatWithHR => 'Chat With HR';

  @override
  String get aboutUs => 'About Us';

  @override
  String get termsOfUse => 'Terms of Use';

  @override
  String get about => 'About';

  @override
  String get newGroup => 'New Group';

  @override
  String get newChannel => 'New Channel';

  @override
  String get ai => 'AI';

  @override
  String get requestToChatWithHR => 'Request to Chat with HR';

  @override
  String get hrGreeting => 'Hello! How can I help you today?';

  @override
  String get hrSampleMessage => 'I wanted to discuss my work schedule.';

  @override
  String get writeAMessage => 'Write a message...';

  @override
  String get hrName => 'HR Manager';

  @override
  String get hrLastSeen => 'Last seen 2 hours ago';

  @override
  String get hrApprovalMessage => 'Your request has been approved.';

  @override
  String get youCreatedGroup => 'You created this group';

  @override
  String get call => 'Call';

  @override
  String get groupName => 'Group Name';

  @override
  String get enterGroupName => 'Enter group name';

  @override
  String get groupDescription => 'Group Description';

  @override
  String get enterGroupDescription => 'Enter group description';

  @override
  String get camera => 'Camera';

  @override
  String get record => 'Record';

  @override
  String get contact => 'Contact';

  @override
  String get gallery => 'Gallery';

  @override
  String get myLocation => 'My Location';

  @override
  String get document => 'Document';

  @override
  String get noContactsFound => 'No contacts found';

  @override
  String get createTask => 'Create Task';

  @override
  String get createProject => 'Create Project';

  @override
  String get allProjects => 'All Projects';

  @override
  String get manageProjects => 'Manage Projects';

  @override
  String get noTasksFound => 'No Tasks Found';

  @override
  String get pleaseCreateATask => 'Please create a task';

  @override
  String get noProjectFound => 'No Project Found';

  @override
  String get pleaseCreateAProject => 'Please create a project';

  @override
  String get comments => 'Comments';

  @override
  String get canIGetMoreInfo => 'Can I get more info?';

  @override
  String get statusUpdate => 'Status Update';

  @override
  String get taskCompleted => 'Task Completed';

  @override
  String get needHelp => 'Need Help';

  @override
  String get approveTask => 'Approve Task';

  @override
  String get addComment => 'Add Comment';

  @override
  String get taskDetails => 'Task Details';

  @override
  String get title => 'Title';

  @override
  String get description => 'Description';

  @override
  String get assignee => 'Assignee';

  @override
  String get status => 'Status';

  @override
  String get completedTasks => 'Completed Tasks';

  @override
  String get clockIn => 'Clock in';

  @override
  String get clockOut => 'Clock Out';

  @override
  String get clockInTime => 'Clock in Time';

  @override
  String get totalShiftTime => 'Total Shift Time';

  @override
  String get overTime => 'Overtime Hours:';

  @override
  String get breakTime => 'Break Time:';

  @override
  String get delay => 'Delay:';

  @override
  String get timeSheet => 'TimeSheet';

  @override
  String get customDateRange => 'Custom Date Range';

  @override
  String get location => 'Location';

  @override
  String get schedule => 'Schedule';

  @override
  String get conferenceCenter => 'Conference Center';

  @override
  String get conferenceDate => 'March 15, 2024';

  @override
  String get conferenceTime => '10:00 AM - 2:00 PM';

  @override
  String get conferenceLocation => 'Main Conference Hall';

  @override
  String get salary => 'Salary';

  @override
  String get thisMonthsSalary => 'This Month\'s Salary';

  @override
  String get netSalary => 'Net Salary';

  @override
  String get expenses => 'Expenses';

  @override
  String get expenseTitle => 'Monthly Expenses';

  @override
  String get expensesAmount => '500,000';

  @override
  String get expensesDate => 'December 2024';

  @override
  String get amountIQD => 'Amount IQD';

  @override
  String get attachment => 'Attachment';

  @override
  String get fileSelected => 'File Selected';

  @override
  String get otherDocuments => 'Other Documents';

  @override
  String get clickToUpload => 'Click to Upload';

  @override
  String get maxFileSize => 'Max file size: 10MB';

  @override
  String get selectDocument => 'Select Document';

  @override
  String get selectDocuments => 'Select Documents';

  @override
  String get resignationRequest => 'Resignation Request';

  @override
  String get waitingManagerApproval => 'Waiting for Manager Approval';

  @override
  String get feedback => 'Feedback';

  @override
  String get managersFeedback => 'Manager\'s Feedback';

  @override
  String get feedbackTime => 'Feedback Time';

  @override
  String get feedbackMessage => 'Feedback Message';

  @override
  String get performance => 'Performance';

  @override
  String get timeOffTaken => 'Time Off Taken';

  @override
  String get holidays => 'Holidays';

  @override
  String get other => 'Other';

  @override
  String get typing => 'Typing...';

  @override
  String get online => 'Online';

  @override
  String get lastSeenOn => 'Last seen on';

  @override
  String get addEmployee => 'Add Employee';

  @override
  String get members => 'Members';

  @override
  String memberCount(int count) {
    return '$count Members';
  }

  @override
  String get pleaseEnterGroupName => 'Please enter a group name';

  @override
  String get failedToSelectImage => 'Failed to select image. Please try again.';

  @override
  String get newChannelFunctionality => 'New Channel functionality will be implemented';

  @override
  String get all => 'All';

  @override
  String get unread => 'Unread';

  @override
  String get teams => 'Teams';

  @override
  String get delivered => 'Delivered';

  @override
  String get read => 'Read';

  @override
  String get selectContacts => 'Select Contacts';

  @override
  String get sendYourLiveLocation => 'Send Your Live Location';

  @override
  String get nearbyPlaces => 'Nearby Places';

  @override
  String get sendYourCurrentLocation => 'Send Your Current Location';

  @override
  String get approximateDistance => 'Approximate to 14 metres';

  @override
  String get yesterday => 'Yesterday';

  @override
  String get days => 'Days';

  @override
  String get hours => 'Hours';

  @override
  String get thisMonth => 'this month';

  @override
  String get jobTitle => 'Job Title';

  @override
  String get country => 'Country';

  @override
  String get dateOfBirth => 'Date of Birth';

  @override
  String get iraq => 'Iraq';

  @override
  String get india => 'India';

  @override
  String get adminContactMessage => 'If you wish to edit, please contact the administration';

  @override
  String get logoutConfirmation => 'Are You Sure You Want to Log Out?';

  @override
  String get chatRequestConfirmation => 'Are You Sure You Want to Send the Request?';

  @override
  String get reasonForResignation => 'Reason for Resignation';

  @override
  String get additionalNotes => 'Additional Notes';

  @override
  String get lastWorkingDay => 'Last Working Day';

  @override
  String get submitResignation => 'Submit Resignation';

  @override
  String overtimeHours(int hours) {
    return '$hours Hours this month';
  }

  @override
  String get performanceAndAchievements => 'Performance & Achievements';

  @override
  String get clickToUploadFile => 'Click to Upload';

  @override
  String get maxFileSizeLimit => '(Max file size 25MB)';

  @override
  String get figmaSubscription => 'Figma Subscription';

  @override
  String get startWork => 'Start Work';

  @override
  String get endWork => 'End Work';

  @override
  String get shiftDetails => 'Shift Details';

  @override
  String get drawSignature => 'Draw Signature';

  @override
  String get addNote => 'Add a Note';

  @override
  String get totalHours => 'Total Hours';

  @override
  String get successfullyCompletedShift => 'Successfully completed your shift';

  @override
  String get clockOutTime => 'Clock Out Time';

  @override
  String get ifYouClockOutNow => 'If You Clock Out Now, You\'ll be Clock Out at';

  @override
  String get confirmHours => 'Confirm Hours';

  @override
  String get myTasks => 'My Tasks';

  @override
  String get showAll => 'Show All';

  @override
  String get requestLeave => 'Request for Leave';

  @override
  String get timesheet => 'Timesheet';

  @override
  String get requestExpenses => 'Request For Expenses';

  @override
  String get submit => 'Submit';

  @override
  String get allDay => 'All Day';

  @override
  String get thisMonthFilter => 'This Month';

  @override
  String get thisWeek => 'This Week';

  @override
  String get sinceStartOfYear => 'Since Start of Year';

  @override
  String get from => 'From';

  @override
  String get to => 'To';

  @override
  String get payForThisPeriod => 'Pay for this period';

  @override
  String get clockInLabel => 'Clock In';

  @override
  String get clockOutLabel => 'Clock Out';

  @override
  String get breakTimeLabel => 'Break Time';

  @override
  String get sendShiftChangeRequest => 'Send Shift Change Request';

  @override
  String get modelAnswer => 'Model Answer';

  @override
  String get design => 'Design';

  @override
  String get backlog => 'Backlog';

  @override
  String get news => 'News';

  @override
  String get employeeOfTheMonth => 'Employee of the Month';

  @override
  String get publishedBy => 'Published By HR Manager';

  @override
  String get today0800Am => 'Today 08:00 AM';

  @override
  String get howardTheodore => 'HOWARD THEODORE';

  @override
  String get descriptionText => 'We are pleased to introduce our new and improved Health Insurance Package, designed with your wellbeing in mind.\n\nAs part of our ongoing commitment to support the health and security of our employees, we have partnered with a new provider to offer a more comprehensive medical plan. This updated package includes:\n\n• Enhanced hospital coverage, including specialist care and private rooms\n• Increased outpatient benefits and faster claim processing\n• Coverage for mental health and wellness services\n• Improved support for family members (spouse and children)\n• A simplified registration and claim procedure via the HR portal';

  @override
  String get paySlip => 'Pay Slip.pdf';

  @override
  String get maxFileSizePdf => '200 KB';

  @override
  String get enterYourNoteHere => 'Enter Your Note Here';

  @override
  String get companyNews => 'Company News';
}
