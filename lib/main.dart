import 'dart:io';

import 'package:ako_basma/util/hive/hive_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_libphonenumber/flutter_libphonenumber.dart'
    as libPhoneNumber;
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';

import 'l10n/generated/app_localizations.dart';

import 'styles/theme.dart';
import 'providers/theme/theme_provider.dart';
import 'providers/language/language_provider.dart';

import 'util/router/router.dart';

late final BaseDeviceInfo kDeviceInfo;
PackageInfo? kPackageInfo;
void main() async {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  // Hive.registerAdapter(ProfileAdapter());
  await Hive.initFlutter();
  await Hive.openBox(HiveUtils.accBoxKey);

  // SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
  //   systemNavigationBarColor: AppColors.gray500,
  // ));

  // to get device ids if needed.
  DeviceInfoPlugin infoPlugin = DeviceInfoPlugin();
  kDeviceInfo = Platform.isAndroid
      ? await infoPlugin.androidInfo
      : await infoPlugin.iosInfo;

  // for firebase init
  // await Firebase.initializeApp(
  //   options: DefaultFirebaseOptions.currentPlatform,
  // );

  // for phone number formatter lib
  await libPhoneNumber.init();

  // for package info, to get things like eg. app name and version
  try {
    kPackageInfo = await PackageInfo.fromPlatform();
  } catch (e) {
    print('Error in Package info - $e');
  }

  runApp(const ProviderScope(child: MyApp()));
  FlutterNativeSplash.remove();
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    MaterialTheme theme = const MaterialTheme();
    final router = ref.watch(routerProvider);

    final themeMode = ref.watch(themeModeProvider);
    final locale = ref.watch(languageProvider);

    return MaterialApp.router(
      title: 'Ako Basma',
      debugShowCheckedModeBanner: false,
      themeMode: themeMode,
      darkTheme: theme.dark(),
      theme: theme.light(),
      routerConfig: router,
      locale: locale, // Use locale from language provider
      // Localization support
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en'), // English
        Locale('ar'), // Arabic
      ],
    );
  }
}
